const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function countData() {
  try {
    console.log('🔍 Counting data in the database...\n')

    // Count cabinets
    const cabinetCount = await prisma.cabinet.count()
    const activeCabinetCount = await prisma.cabinet.count({
      where: { isActive: true }
    })

    // Count teachers
    const teacherCount = await prisma.teacher.count()
    
    // Count groups
    const groupCount = await prisma.group.count()
    const activeGroupCount = await prisma.group.count({
      where: { isActive: true }
    })

    // Get detailed cabinet info
    const cabinets = await prisma.cabinet.findMany({
      select: {
        id: true,
        name: true,
        number: true,
        capacity: true,
        branch: true,
        isActive: true,
        _count: {
          select: {
            groups: true
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    // Get detailed teacher info
    const teachers = await prisma.teacher.findMany({
      select: {
        id: true,
        subject: true,
        branch: true,
        tier: true,
        user: {
          select: {
            name: true,
            phone: true
          }
        },
        _count: {
          select: {
            groups: true
          }
        }
      },
      orderBy: { createdAt: 'asc' }
    })

    // Get detailed group info
    const groups = await prisma.group.findMany({
      select: {
        id: true,
        name: true,
        branch: true,
        isActive: true,
        capacity: true,
        room: true,
        course: {
          select: {
            name: true,
            level: true
          }
        },
        teacher: {
          select: {
            user: {
              select: {
                name: true
              }
            }
          }
        },
        cabinet: {
          select: {
            name: true,
            number: true
          }
        },
        _count: {
          select: {
            currentStudents: true
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    // Display summary
    console.log('📊 SUMMARY:')
    console.log('===========')
    console.log(`📚 Cabinets: ${cabinetCount} total (${activeCabinetCount} active)`)
    console.log(`👨‍🏫 Teachers: ${teacherCount} total`)
    console.log(`👥 Groups: ${groupCount} total (${activeGroupCount} active)`)
    console.log('')

    // Display detailed cabinet info
    console.log('🏢 CABINETS DETAILS:')
    console.log('===================')
    if (cabinets.length === 0) {
      console.log('No cabinets found.')
    } else {
      cabinets.forEach(cabinet => {
        console.log(`• ${cabinet.name} (${cabinet.number}) - Branch: ${cabinet.branch}`)
        console.log(`  Capacity: ${cabinet.capacity}, Groups: ${cabinet._count.groups}, Active: ${cabinet.isActive}`)
      })
    }
    console.log('')

    // Display detailed teacher info
    console.log('👨‍🏫 TEACHERS DETAILS:')
    console.log('=====================')
    if (teachers.length === 0) {
      console.log('No teachers found.')
    } else {
      teachers.forEach(teacher => {
        console.log(`• ${teacher.user.name} (${teacher.user.phone})`)
        console.log(`  Subject: ${teacher.subject}, Branch: ${teacher.branch}, Tier: ${teacher.tier}, Groups: ${teacher._count.groups}`)
      })
    }
    console.log('')

    // Display detailed group info
    console.log('👥 GROUPS DETAILS:')
    console.log('==================')
    if (groups.length === 0) {
      console.log('No groups found.')
    } else {
      groups.forEach(group => {
        const cabinetInfo = group.cabinet ? `${group.cabinet.name} (${group.cabinet.number})` : group.room || 'No room assigned'
        console.log(`• ${group.name} - ${group.course.name} (${group.course.level})`)
        console.log(`  Teacher: ${group.teacher.user.name}, Cabinet/Room: ${cabinetInfo}`)
        console.log(`  Branch: ${group.branch}, Students: ${group._count.currentStudents}, Active: ${group.isActive}`)
      })
    }

    // Branch breakdown
    console.log('')
    console.log('🌿 BRANCH BREAKDOWN:')
    console.log('====================')
    
    const branchStats = {}
    
    // Count by branch
    cabinets.forEach(cabinet => {
      if (!branchStats[cabinet.branch]) {
        branchStats[cabinet.branch] = { cabinets: 0, teachers: 0, groups: 0 }
      }
      branchStats[cabinet.branch].cabinets++
    })
    
    teachers.forEach(teacher => {
      if (!branchStats[teacher.branch]) {
        branchStats[teacher.branch] = { cabinets: 0, teachers: 0, groups: 0 }
      }
      branchStats[teacher.branch].teachers++
    })
    
    groups.forEach(group => {
      if (!branchStats[group.branch]) {
        branchStats[group.branch] = { cabinets: 0, teachers: 0, groups: 0 }
      }
      branchStats[group.branch].groups++
    })

    Object.entries(branchStats).forEach(([branch, stats]) => {
      console.log(`${branch}: ${stats.cabinets} cabinets, ${stats.teachers} teachers, ${stats.groups} groups`)
    })

  } catch (error) {
    console.error('❌ Error counting data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

countData()
