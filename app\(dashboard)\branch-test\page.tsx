"use client"

import { useSession } from "next-auth/react"
import { useBranch } from "@/contexts/branch-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { BranchSwitcher } from "@/components/ui/branch-switcher"
import { Building2, Users, MapPin, Phone, CheckCircle, AlertCircle } from "lucide-react"

export default function BranchTestPage() {
  const { data: session } = useSession()
  const { currentBranch, branches, switchBranch, isLoading } = useBranch()
  const userRole = session?.user?.role

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center space-y-4">
          <Building2 className="h-8 w-8 animate-pulse text-primary" />
          <p className="text-sm text-muted-foreground">Loading branch information...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold flex items-center">
            <Building2 className="h-8 w-8 mr-3 text-primary" />
            Branch Management Test
          </h1>
          <p className="text-muted-foreground">
            Test the branch switching functionality and view current branch information
          </p>
        </div>
      </div>

      {/* Current User & Role Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Current User Information
          </CardTitle>
          <CardDescription>
            Your role and branch access permissions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground">User Name</label>
              <p className="font-semibold">{session?.user?.name || 'Unknown'}</p>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground">Role</label>
              <Badge variant={userRole === 'ADMIN' ? 'default' : 'secondary'}>
                {userRole}
              </Badge>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground">Branch Switching</label>
              <div className="flex items-center gap-2">
                {userRole === 'ADMIN' ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-green-600">Enabled</span>
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-4 w-4 text-amber-600" />
                    <span className="text-sm text-amber-600">Restricted</span>
                  </>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Branch Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building2 className="h-5 w-5 mr-2" />
            Current Branch
          </CardTitle>
          <CardDescription>
            Information about your currently selected branch
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">Branch Name</label>
                <p className="text-lg font-semibold">{currentBranch.name}</p>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">Branch ID</label>
                <code className="px-2 py-1 bg-muted rounded text-sm">{currentBranch.id}</code>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">Status</label>
                <Badge variant={currentBranch.isActive ? 'default' : 'destructive'}>
                  {currentBranch.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
            <div className="space-y-4">
              {currentBranch.address && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground flex items-center">
                    <MapPin className="h-4 w-4 mr-1" />
                    Address
                  </label>
                  <p className="text-sm">{currentBranch.address}</p>
                </div>
              )}
              {currentBranch.phone && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground flex items-center">
                    <Phone className="h-4 w-4 mr-1" />
                    Phone
                  </label>
                  <p className="text-sm">{currentBranch.phone}</p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Branch Switcher Component */}
      {userRole === 'ADMIN' && (
        <Card>
          <CardHeader>
            <CardTitle>Branch Switcher Component</CardTitle>
            <CardDescription>
              Use this component to switch between different branches (Admin only)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center p-8 border-2 border-dashed border-border rounded-lg">
              <BranchSwitcher />
            </div>
          </CardContent>
        </Card>
      )}

      {/* All Available Branches */}
      <Card>
        <CardHeader>
          <CardTitle>Available Branches</CardTitle>
          <CardDescription>
            All branches in the system and their information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {branches.map((branch) => (
              <div
                key={branch.id}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  currentBranch.id === branch.id
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:border-primary/50'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-primary" />
                      <h3 className="font-semibold">{branch.name}</h3>
                      {currentBranch.id === branch.id && (
                        <Badge variant="default" className="text-xs">Current</Badge>
                      )}
                    </div>
                    {branch.address && (
                      <p className="text-sm text-muted-foreground flex items-center">
                        <MapPin className="h-3 w-3 mr-1" />
                        {branch.address}
                      </p>
                    )}
                    {branch.phone && (
                      <p className="text-sm text-muted-foreground flex items-center">
                        <Phone className="h-3 w-3 mr-1" />
                        {branch.phone}
                      </p>
                    )}
                  </div>
                  {userRole === 'ADMIN' && currentBranch.id !== branch.id && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => switchBranch(branch.id)}
                    >
                      Switch
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How Branch Switching Works</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium">For Admin Users:</h4>
            <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1">
              <li>Can switch between any available branch using the branch switcher</li>
              <li>Branch selection is saved in localStorage and persists across sessions</li>
              <li>All data (students, payments, etc.) will be filtered by the selected branch</li>
              <li>Branch switcher appears in the top header and sidebar</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium">For Other Roles:</h4>
            <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1">
              <li>Automatically assigned to a specific branch based on their user profile</li>
              <li>Cannot switch branches - this ensures data security and access control</li>
              <li>Only see data from their assigned branch</li>
              <li>Branch switcher component is hidden for non-admin users</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
