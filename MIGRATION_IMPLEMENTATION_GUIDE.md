# Migration Implementation Guide

## Quick Start Commands

### 1. Start Migration Analysis
```bash
# Navigate to project root
cd /path/to/inno-crm

# Create migration directory
mkdir -p scripts/migration

# Run data analysis
npm run migration:analyze
```

### 2. Execute Migration (Dry Run)
```bash
# Test migration without committing changes
npm run migration:dry-run

# Review migration report
cat migration-report.json
```

### 3. Execute Full Migration
```bash
# Backup database first
npm run db:backup

# Run full migration
npm run migration:execute

# Verify results
npm run migration:verify
```

## Implementation Priority

### Phase 1: Critical Foundation (Start Here)
1. **Analyze JSON Data Integrity** ⭐ HIGH PRIORITY
   - Identifies all data issues before migration
   - Prevents foreign key violations
   - Estimates migration complexity

2. **Create Data Validation Report** ⭐ HIGH PRIORITY
   - Documents all required fixes
   - Guides schema extension decisions
   - Provides migration roadmap

### Phase 2: Infrastructure Setup
3. **Schema Extensions** (if needed)
4. **Data Preparation Scripts**
5. **Migration Tracking Setup**

### Phase 3: Data Migration (Sequential)
6. **Cabinet Migration** (no dependencies)
7. **Teacher Migration** (User + Teacher creation)
8. **Course Creation** (derived from class analysis)
9. **Group Migration** (depends on all above)

### Phase 4: Validation & Cleanup
10. **Data Validation**
11. **Cleanup & Documentation**

## Key Data Issues to Address

### 1. Teacher Data Issues
```json
// Problem: Invalid/placeholder emails
"email": "email@01"  // Not a valid email
"email": "email@02"  // Duplicate pattern

// Solution: Generate unique emails
"email": "<EMAIL>"
```

### 2. Phone Number Issues
```json
// Problem: Inconsistent formats
"phone": "99-999-99-99"           // Dashes
"phone": "+99897 285 88 86"       // Spaces
"phone": "  +99897 931 39 93"     // Leading spaces

// Solution: Normalize to +998XXXXXXXXX
"phone": "+998999999999"
```

### 3. Cabinet Location Parsing
```json
// Problem: Mixed location formats
"location": "4 этаж"      // Floor only
"location": "4 floor"     // English
"location": "4"           // Number only

// Solution: Extract floor number, set building
"floor": 4,
"building": "Main Building"
```

### 4. Course Creation Logic
```javascript
// Derive courses from class data
const courseKey = `${level}-${subject}-${courseAmount}`;
const courses = {
  "A1-English-387000": {
    name: "General English A1",
    level: "A1",
    price: 387000,
    duration: 12
  },
  "B1-English-494000": {
    name: "General English B1", 
    level: "B1",
    price: 494000,
    duration: 14
  }
  // ... more courses
};
```

## Migration Scripts Structure

```
scripts/
├── migration/
│   ├── analyze.js           # Data analysis
│   ├── validate.js          # Data validation
│   ├── migrate-cabinets.js  # Cabinet migration
│   ├── migrate-teachers.js  # Teacher migration
│   ├── create-courses.js    # Course creation
│   ├── migrate-groups.js    # Group migration
│   ├── verify.js           # Post-migration verification
│   └── utils/
│       ├── data-parser.js   # JSON parsing utilities
│       ├── id-mapper.js     # ID mapping utilities
│       ├── validators.js    # Data validation functions
│       └── normalizers.js   # Data normalization functions
```

## Error Handling Strategy

### 1. Validation Errors
- **Missing References**: Log and skip records with invalid foreign keys
- **Duplicate Data**: Merge or skip duplicates based on business rules
- **Invalid Formats**: Normalize or use defaults with warnings

### 2. Migration Errors
- **Constraint Violations**: Log error and continue with next record
- **Database Errors**: Rollback transaction and retry with fixes
- **Timeout Issues**: Implement batch processing with progress tracking

### 3. Rollback Procedures
```bash
# Restore from backup
npm run db:restore backup-YYYY-MM-DD-HH-mm-ss.sql

# Clear migrated data only
npm run migration:rollback

# Reset to clean state
npm run db:reset && npm run db:seed
```

## Data Quality Metrics

### Success Criteria
- **Cabinet Migration**: 22/22 records (100%)
- **Teacher Migration**: 33/33 users + profiles (100%)
- **Course Creation**: ~15-20 unique courses
- **Group Migration**: 100+/100+ groups (aim for 95%+)

### Acceptable Loss Scenarios
- **Invalid Teacher References**: Skip groups with missing teachers
- **Invalid Cabinet References**: Assign to default cabinet
- **Malformed Schedule Data**: Use default schedule pattern

## Testing Strategy

### 1. Unit Tests
- Test each migration function individually
- Validate data transformation logic
- Test error handling scenarios

### 2. Integration Tests
- Test full migration pipeline
- Verify foreign key relationships
- Test rollback procedures

### 3. Manual Verification
- Check CRM UI functionality
- Verify data relationships in database
- Test basic CRUD operations

## Performance Considerations

### 1. Batch Processing
```javascript
// Process in batches to avoid memory issues
const BATCH_SIZE = 50;
for (let i = 0; i < records.length; i += BATCH_SIZE) {
  const batch = records.slice(i, i + BATCH_SIZE);
  await processBatch(batch);
}
```

### 2. Transaction Management
```javascript
// Use transactions for data consistency
await prisma.$transaction(async (tx) => {
  await tx.user.create(userData);
  await tx.teacher.create(teacherData);
});
```

### 3. Progress Tracking
```javascript
// Log progress for long-running migrations
console.log(`Processed ${processed}/${total} records (${percentage}%)`);
```

## Next Steps

1. **Start with Task 1**: Run data analysis to understand scope
2. **Review findings**: Determine if schema extensions are needed
3. **Create migration scripts**: Build utilities and migration functions
4. **Test thoroughly**: Use dry-run mode extensively
5. **Execute migration**: Run full migration with monitoring
6. **Validate results**: Verify data integrity and functionality

## Support & Troubleshooting

### Common Issues
- **Foreign Key Violations**: Check ID mapping tables
- **Duplicate Constraints**: Review uniqueness validation
- **Performance Issues**: Reduce batch sizes or add indexes

### Debug Commands
```bash
# Check migration logs
tail -f logs/migration.log

# Verify specific data
npm run db:query "SELECT COUNT(*) FROM teachers"

# Check relationships
npm run migration:check-relationships
```
