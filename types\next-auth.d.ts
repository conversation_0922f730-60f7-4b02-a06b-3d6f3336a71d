import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      phone?: string | null
      role?: string | null
      branch?: string | null
      image?: string | null
    }
  }

  interface User {
    id: string
    name?: string | null
    email?: string | null
    phone?: string | null
    role?: string | null
    branch?: string | null
    image?: string | null
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role?: string | null
    branch?: string | null
  }
}
