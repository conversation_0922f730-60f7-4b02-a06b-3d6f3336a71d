'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/card'
import { StatsGrid } from '@/components/stats-grid'
import {
  Users,
  UserPlus,
  GraduationCap,
  TrendingUp,
  TrendingDown,
  Calendar,
  CheckCircle,
  ArrowUpRight,
  BookOpen,
  Loader2,
  Phone,
  UserCheck,
  ClipboardList
} from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'
import { useDashboardStore } from '@/lib/stores/dashboard-store'
import { Button } from '@/components/button'
import Link from 'next/link'

interface ReceptionDashboardStats {
  totalStudents: { count: number; growth: number }
  newLeads: { count: number; growth: number }
  activeGroups: { count: number }
  todayEnrollments: { count: number }
  pendingLeads: { count: number }
  recentLeads: Array<{ name: string; course: string; status: string; time: string; phone: string }>
  upcomingClasses: Array<{ group: string; teacher: string; time: string; room: string }>
  recentEnrollments: Array<{ studentName: string; groupName: string; time: string }>
}

export default function ReceptionDashboard() {
  const { currentBranch } = useBranch()
  const { refreshData } = useDashboardStore()
  const [stats, setStats] = useState<ReceptionDashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchReceptionStats()
    refreshData()
  }, [])

  const fetchReceptionStats = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/dashboard/reception-stats')
      if (!response.ok) {
        throw new Error('Failed to fetch reception dashboard stats')
      }
      const data = await response.json()
      setStats(data)
      setError(null)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching reception dashboard stats:', error)
      }
      setError('Failed to load reception dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchReceptionStats}>Try Again</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 tracking-tight">
            <Phone className="inline h-8 w-8 mr-3 text-green-600" />
            Reception Dashboard - {currentBranch.name}
          </h1>
          <p className="text-gray-600 mt-1">Front desk operations and student management.</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            className="shadow-sm"
            onClick={() => {
              fetchReceptionStats()
              refreshData()
            }}
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            Refresh Data
          </Button>
          <Link href="/dashboard/leads">
            <Button className="shadow-sm">
              <UserPlus className="h-4 w-4 mr-2" />
              Manage Leads
            </Button>
          </Link>
        </div>
      </div>

      {/* Reception Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">New Leads</CardTitle>
              <div className="h-10 w-10 rounded-full bg-green-50 flex items-center justify-center">
                <UserPlus className="h-5 w-5 text-green-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-green-700">{formatNumber(stats?.newLeads.count || 0)}</div>
            <div className={`kpi-change mt-2 ${(stats?.newLeads.growth ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {(stats?.newLeads.growth ?? 0) >= 0 ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              <span>{(stats?.newLeads.growth ?? 0) >= 0 ? '+' : ''}{stats?.newLeads.growth || 0}% this week</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Pending Leads</CardTitle>
              <div className="h-10 w-10 rounded-full bg-orange-50 flex items-center justify-center">
                <Phone className="h-5 w-5 text-orange-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-orange-700">{formatNumber(stats?.pendingLeads.count || 0)}</div>
            <div className="kpi-change mt-2 text-orange-600">
              <Phone className="h-4 w-4" />
              <span>Need follow-up</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Total Students</CardTitle>
              <div className="h-10 w-10 rounded-full bg-blue-50 flex items-center justify-center">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-blue-700">{formatNumber(stats?.totalStudents.count || 0)}</div>
            <div className={`kpi-change mt-2 ${(stats?.totalStudents.growth ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {(stats?.totalStudents.growth ?? 0) >= 0 ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              <span>{(stats?.totalStudents.growth ?? 0) >= 0 ? '+' : ''}{stats?.totalStudents.growth || 0}% growth</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Today&apos;s Enrollments</CardTitle>
              <div className="h-10 w-10 rounded-full bg-purple-50 flex items-center justify-center">
                <UserCheck className="h-5 w-5 text-purple-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-purple-700">{formatNumber(stats?.todayEnrollments.count || 0)}</div>
            <div className="kpi-change mt-2 text-purple-600">
              <CheckCircle className="h-4 w-4" />
              <span>New enrollments</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reception Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <CardTitle className="text-lg font-semibold text-gray-900">Quick Actions</CardTitle>
            <CardDescription>Front desk operations</CardDescription>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-3">
              <Link href="/dashboard/leads">
                <Button variant="outline" className="w-full justify-start">
                  <UserPlus className="h-4 w-4 mr-2" />
                  Manage Leads
                </Button>
              </Link>
              <Link href="/dashboard/students">
                <Button variant="outline" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  Student Records
                </Button>
              </Link>
              <Link href="/dashboard/enrollments">
                <Button variant="outline" className="w-full justify-start">
                  <ClipboardList className="h-4 w-4 mr-2" />
                  Enrollments
                </Button>
              </Link>
              <Link href="/dashboard/groups">
                <Button variant="outline" className="w-full justify-start">
                  <GraduationCap className="h-4 w-4 mr-2" />
                  Manage Groups
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Priority Leads</CardTitle>
                <CardDescription className="mt-1">Leads requiring attention</CardDescription>
              </div>
              <Link href="/dashboard/leads">
                <Button variant="ghost" size="sm" className="text-sm">
                  <ArrowUpRight className="h-4 w-4" />
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-4">
              {stats?.recentLeads && stats.recentLeads.length > 0 ? (
                stats.recentLeads.slice(0, 3).map((lead, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <div>
                      <p className="font-medium text-gray-900">{lead.name}</p>
                      <p className="text-sm text-gray-600">{lead.course}</p>
                      <p className="text-xs text-gray-500">{lead.phone}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500 mb-1">{lead.time}</p>
                      <span className="status-badge status-pending">
                        {lead.status}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <UserPlus className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No pending leads</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Today&apos;s Schedule</CardTitle>
                <CardDescription className="mt-1">Upcoming classes</CardDescription>
              </div>
              <Button variant="ghost" size="sm" className="text-sm">
                <Calendar className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-4">
              {stats?.upcomingClasses && stats.upcomingClasses.length > 0 ? (
                stats.upcomingClasses.slice(0, 3).map((classItem, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <div>
                      <p className="font-medium text-gray-900">{classItem.group}</p>
                      <p className="text-sm text-gray-600">{classItem.teacher}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-blue-600">{classItem.time}</p>
                      <p className="text-sm text-gray-500">{classItem.room}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No classes today</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
