<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Origin UI Component Test - Professional Quality</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: 'hsl(var(--border))',
                        input: 'hsl(var(--input))',
                        ring: 'hsl(var(--ring))',
                        background: 'hsl(var(--background))',
                        foreground: 'hsl(var(--foreground))',
                        primary: {
                            DEFAULT: 'hsl(var(--primary))',
                            foreground: 'hsl(var(--primary-foreground))'
                        },
                        secondary: {
                            DEFAULT: 'hsl(var(--secondary))',
                            foreground: 'hsl(var(--secondary-foreground))'
                        },
                        muted: {
                            DEFAULT: 'hsl(var(--muted))',
                            foreground: 'hsl(var(--muted-foreground))'
                        },
                        accent: {
                            DEFAULT: 'hsl(var(--accent))',
                            foreground: 'hsl(var(--accent-foreground))'
                        },
                        destructive: {
                            DEFAULT: 'hsl(var(--destructive))',
                            foreground: 'hsl(var(--destructive-foreground))'
                        },
                    },
                    boxShadow: {
                        'xs': '0 1px 2px 0 rgb(0 0 0 / 0.05)',
                        'sm': '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
                        'md': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
                        'lg': '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
                        'xl': '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
                        '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
                    },
                    borderRadius: {
                        lg: 'var(--radius)',
                        md: 'calc(var(--radius) - 2px)',
                        sm: 'calc(var(--radius) - 4px)'
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            /* Light mode colors */
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 47.4% 11.2%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }

        .btn {
            @apply inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 outline-none focus-visible:ring-2 focus-visible:ring-ring;
        }
        .btn-primary {
            @apply bg-primary/75 border border-primary-foreground/25 text-primary-foreground shadow-sm shadow-black/5 hover:bg-primary/90;
        }
        .btn-outline {
            @apply border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground;
        }
        .btn-secondary {
            @apply bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80;
        }
        .btn-sm {
            @apply h-8 rounded-md px-3 text-xs;
        }
        .btn-default {
            @apply h-9 px-4 py-2;
        }

        .notification {
            @apply bg-background z-50 max-w-[400px] rounded-md border p-4 shadow-lg relative;
        }

        .input {
            @apply flex h-9 w-full min-w-0 rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-xs transition-[color,box-shadow] outline-none placeholder:text-muted-foreground/50 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px];
        }

        .card {
            @apply rounded-lg border bg-card text-card-foreground shadow-sm;
        }

        .theme-toggle {
            @apply fixed top-4 right-4 z-50 inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10;
        }
    </style>
</head>
<body class="bg-background text-foreground font-sans antialiased">
    <!-- Theme Toggle -->
    <button class="theme-toggle" onclick="toggleTheme()" aria-label="Toggle theme">
        <svg class="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="4"/>
            <path d="m12 2 0 2"/>
            <path d="m12 20 0 2"/>
            <path d="m4.93 4.93 1.41 1.41"/>
            <path d="m17.66 17.66 1.41 1.41"/>
            <path d="m2 12 2 0"/>
            <path d="m20 12 2 0"/>
            <path d="m6.34 17.66-1.41 1.41"/>
            <path d="m19.07 4.93-1.41 1.41"/>
        </svg>
        <svg class="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"/>
        </svg>
    </button>

    <div class="min-h-screen p-8">
        <div class="max-w-6xl mx-auto space-y-8">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold">Origin UI Component Testing</h1>
                <p class="text-muted-foreground text-lg">
                    Professional-grade components with proper shadows, colors, and dark/light mode support
                </p>
            </div>

        <!-- Professional Origin UI Notification Examples -->
        <div class="card p-6">
            <h2 class="text-2xl font-semibold mb-6">Professional Origin UI Notifications</h2>
            <div class="grid gap-6">
                <!-- Warning Notification -->
                <div class="notification">
                    <div class="flex gap-2">
                        <div class="flex grow gap-3">
                            <svg class="mt-0.5 shrink-0 text-amber-500" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/>
                                <path d="M12 9v4"/>
                                <path d="m12 17 .01 0"/>
                            </svg>
                            <div class="flex grow flex-col gap-3">
                                <div class="space-y-1">
                                    <p class="text-sm font-medium">
                                        Something requires your action!
                                    </p>
                                    <p class="text-muted-foreground text-sm">
                                        It conveys that a specific action is needed to resolve or address a situation.
                                    </p>
                                </div>
                                <div>
                                    <button class="btn btn-primary btn-sm">Learn more</button>
                                </div>
                            </div>
                        </div>
                        <button class="group -my-1.5 -me-2 w-8 h-8 shrink-0 p-0 hover:bg-accent rounded-md flex items-center justify-center transition-colors" aria-label="Close notification">
                            <svg width="16" height="16" class="opacity-60 transition-opacity group-hover:opacity-100" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M18 6 6 18"/>
                                <path d="m6 6 12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Success Notification -->
                <div class="notification border-green-200/50 bg-green-50/50 dark:border-green-800/50 dark:bg-green-950/50">
                    <div class="flex gap-2">
                        <div class="flex grow gap-3">
                            <svg class="mt-0.5 shrink-0 text-green-600 dark:text-green-400" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                                <polyline points="22,4 12,14.01 9,11.01"/>
                            </svg>
                            <div class="flex grow flex-col gap-3">
                                <div class="space-y-1">
                                    <p class="text-sm font-medium">
                                        Payment processed successfully
                                    </p>
                                    <p class="text-muted-foreground text-sm">
                                        The student's payment has been recorded and their enrollment is now active.
                                    </p>
                                </div>
                                <div>
                                    <button class="btn btn-outline btn-sm">View details</button>
                                </div>
                            </div>
                        </div>
                        <button class="group -my-1.5 -me-2 w-8 h-8 shrink-0 p-0 hover:bg-accent rounded-md flex items-center justify-center transition-colors" aria-label="Close notification">
                            <svg width="16" height="16" class="opacity-60 transition-opacity group-hover:opacity-100" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M18 6 6 18"/>
                                <path d="m6 6 12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Error Notification -->
                <div class="notification border-red-200/50 bg-red-50/50 dark:border-red-800/50 dark:bg-red-950/50">
                    <div class="flex gap-2">
                        <div class="flex grow gap-3">
                            <svg class="mt-0.5 shrink-0 text-red-600 dark:text-red-400" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"/>
                                <line x1="15" y1="9" x2="9" y2="15"/>
                                <line x1="9" y1="9" x2="15" y2="15"/>
                            </svg>
                            <div class="flex grow flex-col gap-3">
                                <div class="space-y-1">
                                    <p class="text-sm font-medium">
                                        Failed to send SMS
                                    </p>
                                    <p class="text-muted-foreground text-sm">
                                        Unable to send enrollment confirmation SMS. Please check the phone number and try again.
                                    </p>
                                </div>
                                <div>
                                    <button class="btn btn-secondary btn-sm">Retry</button>
                                </div>
                            </div>
                        </div>
                        <button class="group -my-1.5 -me-2 w-8 h-8 shrink-0 p-0 hover:bg-accent rounded-md flex items-center justify-center transition-colors" aria-label="Close notification">
                            <svg width="16" height="16" class="opacity-60 transition-opacity group-hover:opacity-100" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M18 6 6 18"/>
                                <path d="m6 6 12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shadow Testing -->
        <div class="bg-white rounded-lg border p-6 shadow-sm">
            <h2 class="text-xl font-semibold mb-4">Shadow Utility Testing</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="p-4 bg-white rounded-md shadow-xs border">
                    <p class="text-sm font-medium">shadow-xs</p>
                    <p class="text-xs text-gray-500">Very subtle</p>
                </div>
                <div class="p-4 bg-white rounded-md shadow-sm border">
                    <p class="text-sm font-medium">shadow-sm</p>
                    <p class="text-xs text-gray-500">Small shadow</p>
                </div>
                <div class="p-4 bg-white rounded-md shadow-md border">
                    <p class="text-sm font-medium">shadow-md</p>
                    <p class="text-xs text-gray-500">Medium shadow</p>
                </div>
                <div class="p-4 bg-white rounded-md shadow-lg border">
                    <p class="text-sm font-medium">shadow-lg</p>
                    <p class="text-xs text-gray-500">Large shadow</p>
                </div>
            </div>
        </div>

        <!-- Button Testing -->
        <div class="bg-white rounded-lg border p-6 shadow-sm">
            <h2 class="text-xl font-semibold mb-4">Button Testing</h2>
            <div class="space-y-4">
                <div class="flex flex-wrap gap-2">
                    <button class="btn btn-default">Default Button</button>
                    <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 outline-none focus-visible:ring-2 focus-visible:ring-gray-500 border border-gray-300 bg-white shadow-xs hover:bg-gray-50 h-9 px-4 py-2">Outline</button>
                    <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 outline-none focus-visible:ring-2 focus-visible:ring-gray-500 bg-gray-100 text-gray-900 shadow-xs hover:bg-gray-200 h-9 px-4 py-2">Secondary</button>
                    <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 outline-none focus-visible:ring-2 focus-visible:ring-red-500 bg-red-600 text-white shadow-xs hover:bg-red-700 h-9 px-4 py-2">Destructive</button>
                </div>
            </div>
        </div>

        <!-- Input Testing -->
        <div class="bg-white rounded-lg border p-6 shadow-sm">
            <h2 class="text-xl font-semibold mb-4">Input Testing</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <input type="text" placeholder="Default input" class="flex h-9 w-full min-w-0 rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-xs transition-[color,box-shadow] outline-none placeholder:text-gray-500 focus-visible:border-blue-500 focus-visible:ring-2 focus-visible:ring-blue-500/20">
                <input type="search" placeholder="Search..." class="flex h-9 w-full min-w-0 rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-xs transition-[color,box-shadow] outline-none placeholder:text-gray-500 focus-visible:border-blue-500 focus-visible:ring-2 focus-visible:ring-blue-500/20">
                <input type="email" placeholder="Email" class="flex h-9 w-full min-w-0 rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-xs transition-[color,box-shadow] outline-none placeholder:text-gray-500 focus-visible:border-blue-500 focus-visible:ring-2 focus-visible:ring-blue-500/20">
                <input type="password" placeholder="Password" class="flex h-9 w-full min-w-0 rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-xs transition-[color,box-shadow] outline-none placeholder:text-gray-500 focus-visible:border-blue-500 focus-visible:ring-2 focus-visible:ring-blue-500/20">
            </div>
        </div>

        <!-- Comparison -->
        <div class="bg-white rounded-lg border p-6 shadow-sm">
            <h2 class="text-xl font-semibold mb-4">Before vs After Comparison</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-medium mb-3 text-red-600">Before (No shadow-xs)</h3>
                    <div class="bg-white max-w-[400px] rounded-md border p-4">
                        <div class="flex gap-2">
                            <div class="flex grow gap-3">
                                <svg class="mt-0.5 shrink-0 text-amber-500" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/>
                                    <path d="M12 9v4"/>
                                    <path d="m12 17 .01 0"/>
                                </svg>
                                <div class="flex grow flex-col gap-3">
                                    <div class="space-y-1">
                                        <p class="text-sm font-medium">Something requires your action!</p>
                                        <p class="text-gray-600 text-sm">Flat appearance without shadows</p>
                                    </div>
                                    <div>
                                        <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium bg-blue-600 text-white h-8 px-3">Learn more</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
            <!-- Button Examples -->
            <div class="card p-6">
                <h2 class="text-2xl font-semibold mb-6">Professional Buttons</h2>
                <div class="space-y-4">
                    <div class="flex flex-wrap gap-3">
                        <button class="btn btn-primary btn-default">Primary Button</button>
                        <button class="btn btn-outline btn-default">Outline Button</button>
                        <button class="btn btn-secondary btn-default">Secondary Button</button>
                        <button class="btn btn-primary btn-sm">Small Button</button>
                    </div>
                </div>
            </div>

            <!-- Input Examples -->
            <div class="card p-6">
                <h2 class="text-2xl font-semibold mb-6">Professional Inputs</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <input type="text" placeholder="Enter your name" class="input">
                    <input type="email" placeholder="Enter your email" class="input">
                    <input type="search" placeholder="Search..." class="input">
                    <input type="password" placeholder="Password" class="input">
                </div>
            </div>

            <!-- Shadow Demonstration -->
            <div class="card p-6">
                <h2 class="text-2xl font-semibold mb-6">Shadow Utilities Working</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="p-4 bg-card rounded-md shadow-xs border">
                        <p class="text-sm font-medium">shadow-xs</p>
                        <p class="text-xs text-muted-foreground">Very subtle</p>
                    </div>
                    <div class="p-4 bg-card rounded-md shadow-sm border">
                        <p class="text-sm font-medium">shadow-sm</p>
                        <p class="text-xs text-muted-foreground">Small shadow</p>
                    </div>
                    <div class="p-4 bg-card rounded-md shadow-md border">
                        <p class="text-sm font-medium">shadow-md</p>
                        <p class="text-xs text-muted-foreground">Medium shadow</p>
                    </div>
                    <div class="p-4 bg-card rounded-md shadow-lg border">
                        <p class="text-sm font-medium">shadow-lg</p>
                        <p class="text-xs text-muted-foreground">Large shadow</p>
                    </div>
                </div>
            </div>

            <!-- Dark/Light Mode Demo -->
            <div class="card p-6">
                <h2 class="text-2xl font-semibold mb-6">Dark/Light Mode Support</h2>
                <p class="text-muted-foreground mb-4">
                    Click the theme toggle button in the top-right corner to switch between light and dark modes.
                    Notice how all components adapt seamlessly with proper color schemes and contrast.
                </p>
                <div class="flex gap-3">
                    <button class="btn btn-primary btn-default" onclick="setTheme('light')">Light Mode</button>
                    <button class="btn btn-secondary btn-default" onclick="setTheme('dark')">Dark Mode</button>
                    <button class="btn btn-outline btn-default" onclick="setTheme('system')">System</button>
                </div>
            </div>

            <!-- Footer -->
            <div class="text-center py-8">
                <p class="text-muted-foreground">
                    This demonstrates the true quality of Origin UI components with proper shadows, colors, and theme support.
                </p>
            </div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setTheme(newTheme);
        }

        function setTheme(theme) {
            const html = document.documentElement;
            html.classList.remove('light', 'dark');

            if (theme === 'system') {
                const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                html.classList.add(systemTheme);
            } else {
                html.classList.add(theme);
            }

            localStorage.setItem('theme', theme);
        }

        // Initialize theme
        const savedTheme = localStorage.getItem('theme') || 'light';
        setTheme(savedTheme);

        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (localStorage.getItem('theme') === 'system') {
                setTheme('system');
            }
        });
    </script>
</body>
</html>
