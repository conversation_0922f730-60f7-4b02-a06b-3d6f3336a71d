#!/usr/bin/env node

/**
 * Cleanup Migration Data
 * Removes all migrated data to allow for clean re-migration
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function cleanup() {
  console.log('🧹 Cleaning up migration data...');
  
  try {
    // Delete in reverse dependency order
    console.log('   Deleting groups...');
    await prisma.group.deleteMany({});
    
    console.log('   Deleting courses...');
    await prisma.course.deleteMany({});
    
    console.log('   Deleting teachers...');
    await prisma.teacher.deleteMany({});
    
    console.log('   Deleting users (teachers only)...');
    await prisma.user.deleteMany({
      where: {
        role: 'TEACHER'
      }
    });
    
    console.log('   Deleting cabinets...');
    await prisma.cabinet.deleteMany({});
    
    console.log('   Deleting migration logs...');
    await prisma.migrationLog.deleteMany({});
    
    console.log('✅ Cleanup completed successfully!');
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  cleanup().catch(console.error);
}

module.exports = { cleanup };
