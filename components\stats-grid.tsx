import { RiArrowRightUpLine } from "@remixicon/react";
import { cn } from "@/lib/utils";

interface StatsCardProps {
  title: string;
  value: string;
  change: {
    value: string;
    trend: "up" | "down";
  };
  icon: React.ReactNode;
}

export function StatsCard({ title, value, change, icon }: StatsCardProps) {
  const isPositive = change.trend === "up";
  const trendColor = isPositive ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400";

  return (
    <div className="relative p-6 group hover:bg-accent/50 transition-colors duration-200 border-r border-border last:border-r-0">
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <p className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
            {title}
          </p>
          <div className="space-y-1">
            <p className="text-3xl font-bold tracking-tight">{value}</p>
            <div className="flex items-center space-x-1 text-sm">
              <span className={cn("font-medium flex items-center", trendColor)}>
                {isPositive ? "↗" : "↘"} {change.value}
              </span>
              <span className="text-muted-foreground">vs last month</span>
            </div>
          </div>
        </div>
        <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 text-primary">
          {icon}
        </div>
      </div>
    </div>
  );
}

interface StatsGridProps {
  stats: StatsCardProps[];
}

export function StatsGrid({ stats }: StatsGridProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 rounded-lg border border-border bg-card shadow-sm overflow-hidden">
      {stats.map((stat) => (
        <StatsCard key={stat.title} {...stat} />
      ))}
    </div>
  );
}
