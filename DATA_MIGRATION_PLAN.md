# Data Migration Plan: JSON Seed Data to Improved CRM

## Overview
This document outlines the comprehensive migration plan for transferring production data from JSON seed files to the improved CRM database schema. The migration involves 3 main data sources with 150+ records total.

## Data Sources Analysis

### 1. Cabinet Data (22 records)
**Source**: `database seed data/Cabinet (1).json`
**Current Structure**:
```json
{
  "id": "cm7n4pe08000110hm4tc4ocew",
  "name": "400",
  "capacity": 25,
  "equipment": [],
  "status": "available",
  "location": "4 этаж"
}
```

**Target Schema**: Cabinet model
**Mapping Issues**:
- `name` → `number` (cabinet number)
- `location` → `floor` + `building` (parse location string)
- `status` → `isActive` (convert "available" to true)
- `equipment` → `equipment` (convert array to JSON string)
- Missing: `branch` (default to "main")

### 2. Teacher Data (33 records)
**Source**: `database seed data/Teacher (1).json`
**Current Structure**:
```json
{
  "id": "cmc8mk7or0000dk5earhataio",
  "name": "Mukhammadxon Soliev",
  "email": "email@01",
  "phone": "99-999-99-99",
  "subjects": ["English"],
  "qualifications": ["Masters","DELTA"],
  "joinDate": "2025-06-23 04:57:01.703",
  "status": "active"
}
```

**Target Schema**: User + Teacher models
**Mapping Issues**:
- Need to create User record first, then Teacher profile
- `subjects` array → `subject` string (take first subject)
- `qualifications` → store in Teacher notes or separate field
- `joinDate` → `createdAt`
- Generate secure passwords for User accounts
- Normalize phone numbers
- Handle duplicate/invalid emails

### 3. Class/Group Data (100+ records)
**Source**: `database seed data/Class (1).json`
**Current Structure**:
```json
{
  "id": "cmc8owvo30000dnwiqsxsjoo1",
  "name": "B1+ new FULL",
  "teacherId": "cmc8n0zvr0006yiputvovsmy6",
  "subject": "English",
  "level": "B1",
  "stage": "EARLY",
  "language": "UZBEK",
  "cabinetId": "cm7n4rmbc00034r6zcjbiadzu",
  "schedule": [
    {"day": "Monday", "endTime": "10:30", "startTime": "09:00"},
    {"day": "Wednesday", "endTime": "10:30", "startTime": "09:00"},
    {"day": "Friday", "endTime": "10:30", "startTime": "09:00"}
  ],
  "courseAmount": 494000,
  "createdAt": "2025-06-23 06:02:51.43",
  "openingDate": "2025-06-23 06:02:51.43",
  "description": null
}
```

**Target Schema**: Course + Group models
**Mapping Issues**:
- Need to create Course records first based on level + subject + price
- `teacherId` → map to new Teacher IDs
- `cabinetId` → map to new Cabinet IDs
- `schedule` array → JSON string
- `courseAmount` → Course.price
- `stage` + `language` → store in Group notes or extend schema
- `openingDate` → `startDate`
- Calculate `endDate` based on course duration

## Migration Strategy

### Phase 1: Data Analysis & Validation
1. **Analyze data integrity**
   - Check for missing references (teacherId, cabinetId)
   - Identify duplicate records
   - Validate data formats (phones, emails, dates)

2. **Create data mapping tables**
   - Old ID → New ID mappings for each entity
   - Course creation mapping (level + subject + price combinations)

### Phase 2: Schema Preparation
1. **Backup current database**
2. **Create migration tracking table**
3. **Prepare rollback procedures**

### Phase 3: Core Data Migration
1. **Migrate Cabinets** (no dependencies)
2. **Migrate Teachers** (User + Teacher creation)
3. **Create Courses** (derived from class data)
4. **Migrate Groups** (depends on Teachers, Cabinets, Courses)

### Phase 4: Data Validation & Cleanup
1. **Verify all relationships**
2. **Check data consistency**
3. **Generate migration report**
4. **Clean up temporary data**

## Risk Assessment

### High Risk
- **Foreign key violations**: TeacherIds and CabinetIds may not exist
- **Data loss**: Qualifications, stage, language fields not in target schema
- **Phone number conflicts**: Duplicate or invalid phone numbers

### Medium Risk
- **Email conflicts**: Many placeholder emails (email@01, email@02)
- **Schedule format changes**: Array to JSON string conversion
- **Date parsing**: Different date formats in source data

### Low Risk
- **Cabinet location parsing**: Extracting floor from location string
- **Course creation**: Deriving courses from class data
- **Password generation**: Creating secure default passwords

## Mitigation Strategies

1. **Create comprehensive validation scripts**
2. **Implement dry-run mode for testing**
3. **Maintain detailed migration logs**
4. **Create rollback procedures**
5. **Extend schema if needed for missing fields**

## Detailed Task Breakdown

### Task 1: Pre-Migration Analysis (2-3 hours)
**Subtasks:**
1.1. Analyze JSON data integrity
   - Check for null/missing required fields
   - Identify orphaned references (teacherId, cabinetId)
   - Count unique values for course creation

1.2. Create data validation report
   - List all data inconsistencies
   - Document required schema extensions
   - Identify potential conflicts

1.3. Design ID mapping strategy
   - Plan old ID → new ID mapping approach
   - Design course creation logic
   - Plan phone number normalization

### Task 2: Schema Extensions (1-2 hours)
**Subtasks:**
2.1. Extend Teacher model (if needed)
   - Add qualifications field
   - Add notes field for additional data

2.2. Extend Group model (if needed)
   - Add stage field (EARLY, MIDDLE, LATE)
   - Add language field (UZBEK, RUSSIAN, MIXED)
   - Add originalId field for tracking

2.3. Create migration tracking table
   - Track migration progress
   - Store ID mappings
   - Log migration errors

### Task 3: Data Preparation Scripts (2-3 hours)
**Subtasks:**
3.1. Create data parsing utilities
   - JSON file readers
   - Data validation functions
   - Phone number normalization
   - Date parsing utilities

3.2. Create course derivation logic
   - Analyze class data for unique course combinations
   - Generate course records from level + subject + price
   - Map duration based on level

3.3. Create ID mapping utilities
   - Generate new IDs for entities
   - Maintain old ID → new ID mappings
   - Handle duplicate detection

### Task 4: Cabinet Migration (1 hour)
**Subtasks:**
4.1. Parse cabinet location data
   - Extract floor numbers from location strings
   - Handle different location formats
   - Set default building values

4.2. Transform cabinet data
   - Map name → number
   - Convert status → isActive
   - Convert equipment array → JSON string
   - Set default branch = "main"

4.3. Insert cabinet records
   - Batch insert with error handling
   - Log successful migrations
   - Handle constraint violations

### Task 5: Teacher Migration (2-3 hours)
**Subtasks:**
5.1. Normalize teacher data
   - Clean phone numbers (remove spaces, standardize format)
   - Generate unique emails for duplicates
   - Handle missing email addresses

5.2. Create User accounts
   - Generate secure random passwords
   - Set role = "TEACHER"
   - Set branch = "main"
   - Handle phone number conflicts

5.3. Create Teacher profiles
   - Link to User accounts
   - Map subjects array → subject string
   - Store qualifications in notes
   - Set experience and salary defaults

### Task 6: Course Creation (1-2 hours)
**Subtasks:**
6.1. Analyze class data for courses
   - Group by level + subject + courseAmount
   - Generate unique course names
   - Set appropriate durations

6.2. Create course records
   - Insert unique course combinations
   - Set pricing from courseAmount
   - Set descriptions based on level
   - Mark all as active

### Task 7: Group Migration (3-4 hours)
**Subtasks:**
7.1. Map foreign key references
   - Map old teacherId → new Teacher ID
   - Map old cabinetId → new Cabinet ID
   - Map class data → Course ID

7.2. Transform schedule data
   - Convert schedule array → JSON string
   - Validate time formats
   - Handle missing schedule data

7.3. Calculate dates and capacity
   - Set startDate from openingDate
   - Calculate endDate from course duration
   - Set capacity from cabinet or default

7.4. Insert group records
   - Batch insert with relationship validation
   - Handle foreign key violations
   - Log migration errors and successes

### Task 8: Data Validation (1-2 hours)
**Subtasks:**
8.1. Verify data integrity
   - Check all foreign key relationships
   - Validate data consistency
   - Count migrated vs source records

8.2. Generate migration report
   - Summary of migrated records
   - List of errors and warnings
   - Data quality metrics

8.3. Test basic functionality
   - Verify CRM pages load correctly
   - Test basic CRUD operations
   - Check data relationships

### Task 9: Cleanup & Documentation (1 hour)
**Subtasks:**
9.1. Clean up migration artifacts
   - Remove temporary files
   - Archive migration logs
   - Update documentation

9.2. Create rollback procedures
   - Document rollback steps
   - Create database backup restore guide
   - Test rollback procedures

## Estimated Timeline
- **Total Time**: 12-18 hours
- **Critical Path**: Data analysis → Schema prep → Teacher migration → Group migration
- **Parallel Work**: Cabinet migration can run independently
- **Testing Time**: Additional 2-3 hours for thorough testing

## Success Criteria
1. All 150+ records successfully migrated
2. Zero foreign key violations
3. All CRM functionality works with migrated data
4. Complete audit trail of migration process
5. Rollback procedures tested and documented
