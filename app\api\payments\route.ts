import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const paymentSchema = z.object({
  studentId: z.string(),
  amount: z.number().min(0),
  method: z.enum(['CASH', 'CARD']),
  status: z.enum(['PAID', 'DEBT', 'REFUNDED']).default('PAID'),
  description: z.string().optional(),
  learningStartDate: z.string().optional(),
  learningEndDate: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only ADMIN and CASHIER can view payments
    if (!session.user.role || !['ADMIN', 'CASHIER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const method = searchParams.get('method')
    const studentId = searchParams.get('studentId')
    const requestedBranch = searchParams.get('branch')

    // Determine branch based on user role
    const userRole = (session.user as any).role
    const userBranch = (session.user as any).branch
    let branchFilter: string

    if (userRole === 'ADMIN') {
      // ADMIN can view any branch or all branches
      branchFilter = requestedBranch || userBranch || 'main'
    } else {
      // Non-admin users can only see their assigned branch
      branchFilter = userBranch || 'main'
    }

    // Use branch ID directly for database query (data is stored with branch IDs, not names)
    const where: any = {
      // Filter payments by branch through student relationship
      student: {
        branch: branchFilter
      }
    }

    if (search) {
      where.OR = [
        {
          student: {
            branch: branchFilter,
            name: { contains: search, mode: 'insensitive' }
          }
        },
        {
          student: {
            branch: branchFilter,
            phone: { contains: search }
          }
        },
        { transactionId: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (status) {
      where.status = status
    }

    if (method) {
      where.method = method
    }

    if (studentId) {
      where.studentId = studentId
      // Ensure the student belongs to the current branch
      where.student.id = studentId
    }

    const [payments, total] = await Promise.all([
      prisma.payment.findMany({
        where,
        include: {
          student: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true,
              level: true,
              branch: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.payment.count({ where }),
    ])

    return NextResponse.json({
      payments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching payments:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only ADMIN and CASHIER can create payments
    if (!session.user.role || !['ADMIN', 'CASHIER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = paymentSchema.parse(body)

    const payment = await prisma.payment.create({
      data: {
        studentId: validatedData.studentId,
        amount: validatedData.amount,
        method: validatedData.method,
        status: validatedData.status,
        description: validatedData.description,
        // Store learning period in description if provided
        ...(validatedData.learningStartDate && validatedData.learningEndDate && {
          description: `${validatedData.description || ''} (Learning period: ${validatedData.learningStartDate} to ${validatedData.learningEndDate})`.trim()
        }),
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
            level: true,
            branch: true,
          },
        },
      },
    })

    return NextResponse.json(payment, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating payment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
