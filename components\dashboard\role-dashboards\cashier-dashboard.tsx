'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/card'
import { StatsGrid } from '@/components/stats-grid'
import {
  Users,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calendar,
  CheckCircle,
  ArrowUpRight,
  CreditCard,
  Loader2,
  Clock,
  AlertCircle
} from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'
import { useDashboardStore } from '@/lib/stores/dashboard-store'
import { Button } from '@/components/button'
import Link from 'next/link'

interface CashierDashboardStats {
  todayPayments: { count: number; amount: number }
  pendingPayments: { count: number; amount: number }
  totalStudents: { count: number }
  overduePayments: { count: number; amount: number }
  recentPayments: Array<{ studentName: string; amount: number; method: string; time: string; status: string }>
  upcomingDues: Array<{ studentName: string; amount: number; dueDate: string; phone: string }>
}

export default function CashierDashboard() {
  const { currentBranch } = useBranch()
  const { refreshData } = useDashboardStore()
  const [stats, setStats] = useState<CashierDashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchCashierStats()
    refreshData()
  }, [])

  const fetchCashierStats = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/dashboard/cashier-stats')
      if (!response.ok) {
        throw new Error('Failed to fetch cashier dashboard stats')
      }
      const data = await response.json()
      setStats(data)
      setError(null)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching cashier dashboard stats:', error)
      }
      setError('Failed to load cashier dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('uz-UZ', {
      style: 'currency',
      currency: 'UZS',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('UZS', 'UZS')
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchCashierStats}>Try Again</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 tracking-tight">
            <CreditCard className="inline h-8 w-8 mr-3 text-green-600" />
            Cashier Dashboard - {currentBranch.name}
          </h1>
          <p className="text-gray-600 mt-1">Payment processing and financial transactions.</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            className="shadow-sm"
            onClick={() => {
              fetchCashierStats()
              refreshData()
            }}
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            Refresh Data
          </Button>
          <Link href="/dashboard/payments">
            <Button className="shadow-sm">
              <DollarSign className="h-4 w-4 mr-2" />
              Process Payments
            </Button>
          </Link>
        </div>
      </div>

      {/* Cashier Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Today&apos;s Payments</CardTitle>
              <div className="h-10 w-10 rounded-full bg-green-50 flex items-center justify-center">
                <DollarSign className="h-5 w-5 text-green-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-green-700">{formatCurrency(stats?.todayPayments.amount || 0)}</div>
            <div className="kpi-change mt-2 text-green-600">
              <CheckCircle className="h-4 w-4" />
              <span>{formatNumber(stats?.todayPayments.count || 0)} transactions</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Pending Payments</CardTitle>
              <div className="h-10 w-10 rounded-full bg-orange-50 flex items-center justify-center">
                <Clock className="h-5 w-5 text-orange-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-orange-700">{formatCurrency(stats?.pendingPayments.amount || 0)}</div>
            <div className="kpi-change mt-2 text-orange-600">
              <Clock className="h-4 w-4" />
              <span>{formatNumber(stats?.pendingPayments.count || 0)} pending</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Overdue Payments</CardTitle>
              <div className="h-10 w-10 rounded-full bg-red-50 flex items-center justify-center">
                <AlertCircle className="h-5 w-5 text-red-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-red-700">{formatCurrency(stats?.overduePayments.amount || 0)}</div>
            <div className="kpi-change mt-2 text-red-600">
              <AlertCircle className="h-4 w-4" />
              <span>{formatNumber(stats?.overduePayments.count || 0)} overdue</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Active Students</CardTitle>
              <div className="h-10 w-10 rounded-full bg-blue-50 flex items-center justify-center">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-blue-700">{formatNumber(stats?.totalStudents.count || 0)}</div>
            <div className="kpi-change mt-2 text-blue-600">
              <Users className="h-4 w-4" />
              <span>Total students</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Cashier Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <CardTitle className="text-lg font-semibold text-gray-900">Quick Actions</CardTitle>
            <CardDescription>Payment operations</CardDescription>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-3">
              <Link href="/dashboard/payments">
                <Button variant="outline" className="w-full justify-start">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Process Payment
                </Button>
              </Link>
              <Link href="/dashboard/students">
                <Button variant="outline" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  Student Lookup
                </Button>
              </Link>
              <Button variant="outline" className="w-full justify-start" disabled>
                <CreditCard className="h-4 w-4 mr-2" />
                Payment History
              </Button>
              <Button variant="outline" className="w-full justify-start" disabled>
                <AlertCircle className="h-4 w-4 mr-2" />
                Overdue Reports
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Recent Payments</CardTitle>
                <CardDescription className="mt-1">Latest transactions</CardDescription>
              </div>
              <Link href="/dashboard/payments">
                <Button variant="ghost" size="sm" className="text-sm">
                  <ArrowUpRight className="h-4 w-4" />
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-4">
              {stats?.recentPayments && stats.recentPayments.length > 0 ? (
                stats.recentPayments.slice(0, 3).map((payment, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <div>
                      <p className="font-medium text-gray-900">{payment.studentName}</p>
                      <p className="text-sm text-gray-600">{payment.method}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-green-600">{formatCurrency(payment.amount)}</p>
                      <p className="text-sm text-gray-500">{payment.time}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <DollarSign className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No recent payments</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Upcoming Dues</CardTitle>
                <CardDescription className="mt-1">Payment reminders</CardDescription>
              </div>
              <Button variant="ghost" size="sm" className="text-sm">
                <Calendar className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-4">
              {stats?.upcomingDues && stats.upcomingDues.length > 0 ? (
                stats.upcomingDues.slice(0, 3).map((due, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <div>
                      <p className="font-medium text-gray-900">{due.studentName}</p>
                      <p className="text-sm text-gray-600">{due.phone}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-orange-600">{formatCurrency(due.amount)}</p>
                      <p className="text-sm text-gray-500">{due.dueDate}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No upcoming dues</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
