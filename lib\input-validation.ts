/**
 * Input Validation & Sanitization - Comprehensive security for user inputs
 * 
 * This module provides sanitization and validation utilities to prevent
 * XSS, SQL injection, and other input-based attacks.
 */

import * as z from 'zod'
import DOMPurify from 'isomorphic-dompurify'

// Common validation patterns
export const VALIDATION_PATTERNS = {
  // Phone number (international format)
  PHONE: /^\+?[1-9]\d{1,14}$/,
  
  // Email (RFC 5322 compliant)
  EMAIL: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
  
  // Strong password (8+ chars, uppercase, lowercase, number, special char)
  STRONG_PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  
  // Alphanumeric with spaces and common punctuation
  SAFE_TEXT: /^[a-zA-Z0-9\s\-_.,!?()]+$/,
  
  // Numbers only
  NUMERIC: /^\d+$/,
  
  // Decimal numbers
  DECIMAL: /^\d+(\.\d{1,2})?$/,
  
  // URL validation
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  
  // UUID validation
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  
  // CUID validation (Prisma default)
  CUID: /^c[a-z0-9]{24}$/
}

/**
 * Sanitize HTML content to prevent XSS attacks
 */
export function sanitizeHtml(input: string): string {
  if (typeof input !== 'string') return ''
  
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true
  })
}

/**
 * Sanitize plain text input
 */
export function sanitizeText(input: string): string {
  if (typeof input !== 'string') return ''
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .slice(0, 1000) // Limit length
}

/**
 * Sanitize and validate phone number
 */
export function sanitizePhone(input: string): string {
  if (typeof input !== 'string') return ''
  
  return input
    .replace(/[^\d+]/g, '') // Keep only digits and +
    .slice(0, 20) // Limit length
}

/**
 * Sanitize and validate email
 */
export function sanitizeEmail(input: string): string {
  if (typeof input !== 'string') return ''
  
  return input
    .toLowerCase()
    .trim()
    .slice(0, 254) // RFC 5321 limit
}

/**
 * Enhanced Zod schemas with sanitization
 */
export const sanitizedSchemas = {
  // Safe string with length limits
  safeString: (min = 1, max = 255) => 
    z.string()
      .min(min, `Must be at least ${min} characters`)
      .max(max, `Must be at most ${max} characters`)
      .transform(sanitizeText)
      .refine(val => VALIDATION_PATTERNS.SAFE_TEXT.test(val), 'Contains invalid characters'),
  
  // Phone number with sanitization
  phone: () =>
    z.string()
      .transform(sanitizePhone)
      .refine(val => VALIDATION_PATTERNS.PHONE.test(val), 'Invalid phone number format'),
  
  // Email with sanitization
  email: () =>
    z.string()
      .transform(sanitizeEmail)
      .refine(val => VALIDATION_PATTERNS.EMAIL.test(val), 'Invalid email format'),
  
  // Strong password
  strongPassword: () =>
    z.string()
      .min(8, 'Password must be at least 8 characters')
      .max(128, 'Password must be at most 128 characters')
      .refine(val => VALIDATION_PATTERNS.STRONG_PASSWORD.test(val), 
        'Password must contain uppercase, lowercase, number, and special character'),
  
  // HTML content with sanitization
  htmlContent: (maxLength = 5000) =>
    z.string()
      .max(maxLength, `Content must be at most ${maxLength} characters`)
      .transform(sanitizeHtml),
  
  // URL validation
  url: () =>
    z.string()
      .refine(val => VALIDATION_PATTERNS.URL.test(val), 'Invalid URL format'),
  
  // UUID validation
  uuid: () =>
    z.string()
      .refine(val => VALIDATION_PATTERNS.UUID.test(val) || VALIDATION_PATTERNS.CUID.test(val), 
        'Invalid ID format'),
  
  // Numeric string
  numericString: () =>
    z.string()
      .refine(val => VALIDATION_PATTERNS.NUMERIC.test(val), 'Must contain only numbers'),
  
  // Decimal string
  decimalString: () =>
    z.string()
      .refine(val => VALIDATION_PATTERNS.DECIMAL.test(val), 'Invalid decimal format'),
  
  // Branch ID validation
  branchId: () =>
    z.enum(['main', 'branch'], { errorMap: () => ({ message: 'Invalid branch ID' }) }),
  
  // Role validation
  role: () =>
    z.enum(['ADMIN', 'MANAGER', 'RECEPTION', 'CASHIER'], 
      { errorMap: () => ({ message: 'Invalid role' }) }),
  
  // Level validation
  level: () =>
    z.enum(['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS'],
      { errorMap: () => ({ message: 'Invalid level' }) }),
  
  // Status validation
  studentStatus: () =>
    z.enum(['ACTIVE', 'DROPPED', 'PAUSED', 'COMPLETED'],
      { errorMap: () => ({ message: 'Invalid student status' }) }),
  
  // Payment method validation
  paymentMethod: () =>
    z.enum(['CASH', 'CARD'],
      { errorMap: () => ({ message: 'Invalid payment method' }) }),
  
  // Payment status validation
  paymentStatus: () =>
    z.enum(['PAID', 'DEBT', 'REFUNDED'],
      { errorMap: () => ({ message: 'Invalid payment status' }) })
}

/**
 * Common validation schemas for API endpoints
 */
export const commonSchemas = {
  // Pagination parameters
  pagination: z.object({
    page: z.coerce.number().min(1).max(1000).default(1),
    limit: z.coerce.number().min(1).max(100).default(10)
  }),
  
  // Search parameters
  search: z.object({
    search: z.string().max(100).optional().transform(val => val ? sanitizeText(val) : undefined)
  }),
  
  // Date range parameters
  dateRange: z.object({
    startDate: z.string().datetime().optional(),
    endDate: z.string().datetime().optional()
  }).refine(data => {
    if (data.startDate && data.endDate) {
      return new Date(data.startDate) <= new Date(data.endDate)
    }
    return true
  }, 'Start date must be before end date'),
  
  // ID parameter
  idParam: z.object({
    id: sanitizedSchemas.uuid()
  })
}

/**
 * Validate and sanitize request body
 */
export function validateRequestBody<T>(
  body: unknown,
  schema: z.ZodSchema<T>
): { success: true; data: T } | { success: false; errors: z.ZodError } {
  try {
    const data = schema.parse(body)
    return { success: true, data }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error }
    }
    throw error
  }
}

/**
 * Validate query parameters
 */
export function validateQueryParams<T>(
  searchParams: URLSearchParams,
  schema: z.ZodSchema<T>
): { success: true; data: T } | { success: false; errors: z.ZodError } {
  const params = Object.fromEntries(searchParams.entries())
  return validateRequestBody(params, schema)
}

/**
 * Create validation error response
 */
export function createValidationErrorResponse(errors: z.ZodError) {
  return {
    error: 'Validation failed',
    details: errors.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code
    }))
  }
}
