#!/usr/bin/env node

/**
 * Data Migration Analysis Script
 * Analyzes JSON seed data for integrity issues and migration planning
 */

const fs = require('fs');
const path = require('path');

// File paths
const DATA_DIR = path.join(process.cwd(), 'database seed data');
const CABINET_FILE = path.join(DATA_DIR, 'Cabinet (1).json');
const TEACHER_FILE = path.join(DATA_DIR, 'Teacher (1).json');
const CLASS_FILE = path.join(DATA_DIR, 'Class (1).json');

// Analysis results
const analysis = {
  summary: {
    totalRecords: 0,
    cabinetCount: 0,
    teacherCount: 0,
    classCount: 0,
    analysisDate: new Date().toISOString()
  },
  cabinets: {
    records: [],
    issues: [],
    locationFormats: new Set(),
    capacityRange: { min: Infinity, max: 0 }
  },
  teachers: {
    records: [],
    issues: [],
    phoneFormats: new Set(),
    emailFormats: new Set(),
    subjects: new Set(),
    qualifications: new Set(),
    duplicatePhones: [],
    duplicateEmails: []
  },
  classes: {
    records: [],
    issues: [],
    levels: new Set(),
    subjects: new Set(),
    stages: new Set(),
    languages: new Set(),
    courseAmounts: new Set(),
    orphanedTeachers: [],
    orphanedCabinets: [],
    scheduleFormats: new Set(),
    uniqueCourses: new Map()
  },
  relationships: {
    validTeacherRefs: 0,
    invalidTeacherRefs: 0,
    validCabinetRefs: 0,
    invalidCabinetRefs: 0
  }
};

/**
 * Load and parse JSON file
 */
function loadJsonFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Error loading ${filePath}:`, error.message);
    return [];
  }
}

/**
 * Analyze cabinet data
 */
function analyzeCabinets(cabinets) {
  console.log('📋 Analyzing Cabinet data...');
  
  analysis.cabinets.records = cabinets;
  analysis.summary.cabinetCount = cabinets.length;

  cabinets.forEach((cabinet, index) => {
    // Check required fields
    if (!cabinet.id) {
      analysis.cabinets.issues.push(`Cabinet ${index}: Missing ID`);
    }
    if (!cabinet.name) {
      analysis.cabinets.issues.push(`Cabinet ${index}: Missing name`);
    }
    if (!cabinet.capacity || cabinet.capacity <= 0) {
      analysis.cabinets.issues.push(`Cabinet ${index}: Invalid capacity: ${cabinet.capacity}`);
    }

    // Collect location formats
    if (cabinet.location) {
      analysis.cabinets.locationFormats.add(cabinet.location);
    }

    // Track capacity range
    if (cabinet.capacity) {
      analysis.cabinets.capacityRange.min = Math.min(analysis.cabinets.capacityRange.min, cabinet.capacity);
      analysis.cabinets.capacityRange.max = Math.max(analysis.cabinets.capacityRange.max, cabinet.capacity);
    }

    // Check equipment format
    if (cabinet.equipment && !Array.isArray(cabinet.equipment)) {
      analysis.cabinets.issues.push(`Cabinet ${cabinet.name}: Equipment is not an array`);
    }
  });

  console.log(`   ✓ Found ${cabinets.length} cabinet records`);
  console.log(`   ✓ Capacity range: ${analysis.cabinets.capacityRange.min} - ${analysis.cabinets.capacityRange.max}`);
  console.log(`   ✓ Location formats: ${analysis.cabinets.locationFormats.size} unique`);
}

/**
 * Analyze teacher data
 */
function analyzeTeachers(teachers) {
  console.log('👨‍🏫 Analyzing Teacher data...');
  
  analysis.teachers.records = teachers;
  analysis.summary.teacherCount = teachers.length;

  const phoneMap = new Map();
  const emailMap = new Map();

  teachers.forEach((teacher, index) => {
    // Check required fields
    if (!teacher.id) {
      analysis.teachers.issues.push(`Teacher ${index}: Missing ID`);
    }
    if (!teacher.name) {
      analysis.teachers.issues.push(`Teacher ${index}: Missing name`);
    }
    if (!teacher.phone) {
      analysis.teachers.issues.push(`Teacher ${index}: Missing phone`);
    }

    // Collect phone formats and check duplicates
    if (teacher.phone) {
      analysis.teachers.phoneFormats.add(teacher.phone);
      const normalizedPhone = teacher.phone.replace(/[\s\-\(\)]/g, '');
      if (phoneMap.has(normalizedPhone)) {
        analysis.teachers.duplicatePhones.push({
          phone: teacher.phone,
          teachers: [phoneMap.get(normalizedPhone), teacher.name]
        });
      } else {
        phoneMap.set(normalizedPhone, teacher.name);
      }
    }

    // Collect email formats and check duplicates
    if (teacher.email) {
      analysis.teachers.emailFormats.add(teacher.email);
      if (emailMap.has(teacher.email)) {
        analysis.teachers.duplicateEmails.push({
          email: teacher.email,
          teachers: [emailMap.get(teacher.email), teacher.name]
        });
      } else {
        emailMap.set(teacher.email, teacher.name);
      }
    }

    // Collect subjects and qualifications
    if (teacher.subjects && Array.isArray(teacher.subjects)) {
      teacher.subjects.forEach(subject => analysis.teachers.subjects.add(subject));
    }
    if (teacher.qualifications && Array.isArray(teacher.qualifications)) {
      teacher.qualifications.forEach(qual => analysis.teachers.qualifications.add(qual));
    }

    // Validate email format
    if (teacher.email && !teacher.email.includes('@')) {
      analysis.teachers.issues.push(`Teacher ${teacher.name}: Invalid email format: ${teacher.email}`);
    }

    // Check for placeholder emails
    if (teacher.email && teacher.email.match(/^email@\d+$/)) {
      analysis.teachers.issues.push(`Teacher ${teacher.name}: Placeholder email: ${teacher.email}`);
    }
  });

  console.log(`   ✓ Found ${teachers.length} teacher records`);
  console.log(`   ✓ Subjects: ${Array.from(analysis.teachers.subjects).join(', ')}`);
  console.log(`   ✓ Qualifications: ${Array.from(analysis.teachers.qualifications).join(', ')}`);
  console.log(`   ✓ Duplicate phones: ${analysis.teachers.duplicatePhones.length}`);
  console.log(`   ✓ Duplicate emails: ${analysis.teachers.duplicateEmails.length}`);
}

/**
 * Analyze class data
 */
function analyzeClasses(classes, teachers, cabinets) {
  console.log('📚 Analyzing Class data...');
  
  analysis.classes.records = classes;
  analysis.summary.classCount = classes.length;

  const teacherIds = new Set(teachers.map(t => t.id));
  const cabinetIds = new Set(cabinets.map(c => c.id));

  classes.forEach((classItem, index) => {
    // Check required fields
    if (!classItem.id) {
      analysis.classes.issues.push(`Class ${index}: Missing ID`);
    }
    if (!classItem.name) {
      analysis.classes.issues.push(`Class ${index}: Missing name`);
    }
    if (!classItem.teacherId) {
      analysis.classes.issues.push(`Class ${index}: Missing teacherId`);
    }

    // Check teacher reference
    if (classItem.teacherId) {
      if (teacherIds.has(classItem.teacherId)) {
        analysis.relationships.validTeacherRefs++;
      } else {
        analysis.relationships.invalidTeacherRefs++;
        analysis.classes.orphanedTeachers.push({
          classId: classItem.id,
          className: classItem.name,
          teacherId: classItem.teacherId
        });
      }
    }

    // Check cabinet reference
    if (classItem.cabinetId) {
      if (cabinetIds.has(classItem.cabinetId)) {
        analysis.relationships.validCabinetRefs++;
      } else {
        analysis.relationships.invalidCabinetRefs++;
        analysis.classes.orphanedCabinets.push({
          classId: classItem.id,
          className: classItem.name,
          cabinetId: classItem.cabinetId
        });
      }
    }

    // Collect metadata
    if (classItem.level) analysis.classes.levels.add(classItem.level);
    if (classItem.subject) analysis.classes.subjects.add(classItem.subject);
    if (classItem.stage) analysis.classes.stages.add(classItem.stage);
    if (classItem.language) analysis.classes.languages.add(classItem.language);
    if (classItem.courseAmount) analysis.classes.courseAmounts.add(classItem.courseAmount);

    // Analyze schedule format
    if (classItem.schedule) {
      if (Array.isArray(classItem.schedule)) {
        analysis.classes.scheduleFormats.add('array');
      } else if (typeof classItem.schedule === 'string') {
        analysis.classes.scheduleFormats.add('string');
      } else {
        analysis.classes.scheduleFormats.add('other');
      }
    }

    // Generate unique course combinations
    if (classItem.level && classItem.subject && classItem.courseAmount) {
      const courseKey = `${classItem.level}-${classItem.subject}-${classItem.courseAmount}`;
      if (!analysis.classes.uniqueCourses.has(courseKey)) {
        analysis.classes.uniqueCourses.set(courseKey, {
          level: classItem.level,
          subject: classItem.subject,
          price: classItem.courseAmount,
          count: 0
        });
      }
      analysis.classes.uniqueCourses.get(courseKey).count++;
    }
  });

  console.log(`   ✓ Found ${classes.length} class records`);
  console.log(`   ✓ Levels: ${Array.from(analysis.classes.levels).join(', ')}`);
  console.log(`   ✓ Subjects: ${Array.from(analysis.classes.subjects).join(', ')}`);
  console.log(`   ✓ Stages: ${Array.from(analysis.classes.stages).join(', ')}`);
  console.log(`   ✓ Languages: ${Array.from(analysis.classes.languages).join(', ')}`);
  console.log(`   ✓ Unique courses: ${analysis.classes.uniqueCourses.size}`);
  console.log(`   ✓ Orphaned teachers: ${analysis.classes.orphanedTeachers.length}`);
  console.log(`   ✓ Orphaned cabinets: ${analysis.classes.orphanedCabinets.length}`);
}

/**
 * Main analysis function
 */
async function runAnalysis() {
  console.log('🔍 Starting Data Migration Analysis...\n');

  // Load data files
  const cabinets = loadJsonFile(CABINET_FILE);
  const teachers = loadJsonFile(TEACHER_FILE);
  const classes = loadJsonFile(CLASS_FILE);

  analysis.summary.totalRecords = cabinets.length + teachers.length + classes.length;

  // Run analysis
  analyzeCabinets(cabinets);
  console.log('');
  analyzeTeachers(teachers);
  console.log('');
  analyzeClasses(classes, teachers, cabinets);

  // Convert Sets and Maps to arrays for JSON serialization
  const serializedAnalysis = {
    ...analysis,
    cabinets: {
      ...analysis.cabinets,
      locationFormats: Array.from(analysis.cabinets.locationFormats)
    },
    teachers: {
      ...analysis.teachers,
      phoneFormats: Array.from(analysis.teachers.phoneFormats),
      emailFormats: Array.from(analysis.teachers.emailFormats),
      subjects: Array.from(analysis.teachers.subjects),
      qualifications: Array.from(analysis.teachers.qualifications)
    },
    classes: {
      ...analysis.classes,
      levels: Array.from(analysis.classes.levels),
      subjects: Array.from(analysis.classes.subjects),
      stages: Array.from(analysis.classes.stages),
      languages: Array.from(analysis.classes.languages),
      courseAmounts: Array.from(analysis.classes.courseAmounts),
      scheduleFormats: Array.from(analysis.classes.scheduleFormats),
      uniqueCourses: Array.from(analysis.classes.uniqueCourses.entries()).map(([key, value]) => ({
        key,
        ...value
      }))
    }
  };

  // Save analysis results
  const outputFile = path.join(process.cwd(), 'migration-analysis.json');
  fs.writeFileSync(outputFile, JSON.stringify(serializedAnalysis, null, 2));

  console.log('\n📊 Analysis Summary:');
  console.log(`   Total Records: ${analysis.summary.totalRecords}`);
  console.log(`   Cabinets: ${analysis.summary.cabinetCount}`);
  console.log(`   Teachers: ${analysis.summary.teacherCount}`);
  console.log(`   Classes: ${analysis.summary.classCount}`);
  console.log(`   Cabinet Issues: ${analysis.cabinets.issues.length}`);
  console.log(`   Teacher Issues: ${analysis.teachers.issues.length}`);
  console.log(`   Class Issues: ${analysis.classes.issues.length}`);
  console.log(`   Invalid Teacher Refs: ${analysis.relationships.invalidTeacherRefs}`);
  console.log(`   Invalid Cabinet Refs: ${analysis.relationships.invalidCabinetRefs}`);
  
  console.log(`\n✅ Analysis complete! Results saved to: ${outputFile}`);
  
  return serializedAnalysis;
}

// Run analysis if called directly
if (require.main === module) {
  runAnalysis().catch(console.error);
}

module.exports = { runAnalysis };
