{"mode": "live", "startTime": "2025-07-14T11:00:52.547Z", "endTime": "2025-07-14T11:02:47.412Z", "duration": 114.75, "stats": {"cabinets": {"total": 22, "success": 22, "errors": 0}, "teachers": {"total": 34, "success": 0, "errors": 34}, "courses": {"total": 15, "success": 3, "errors": 12}, "groups": {"total": 198, "success": 0, "errors": 198}}, "errors": ["Teacher <PERSON><PERSON><PERSON><PERSON><PERSON> Soliev: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"oo98xo4ri28uphktpxxtvw52\",\n            name: \"<PERSON><PERSON><PERSON><PERSON>xon Soliev\",\n            email: \"<EMAIL>\",\n            phone: \"+998999999999\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$6TRBvvPVyGo27UjC/Iy6hehhBCEmemC1tVUk.uhnklSZc0KFtHyj.\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"njkohbijgzjd2zr68i9ootxe\",\n            name: \"<PERSON><PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998972858886\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$6iAJxrQpD47sQVpGLVqIlObnj3wWNvzdWH7wGu.zzTUpGfmNqfcQi\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"sfzaeem49j7svpen6iv32upk\",\n            name: \"<PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998979352109\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$es2.WbuWPySweVbVnMHjDevZXJqqaBTPwDcajWP/fS6aR5zGN6Ani\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"ynitl0j52n3o9x5iulxd2r0x\",\n            name: \"<PERSON><PERSON><PERSON><PERSON>\",\n            email: \"teacher.kam<PERSON><PERSON>@innovativecentre.uz\",\n            phone: \"+998979168416\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$Ox/HZGeYYQuP7qpz.ItPl.clpIpu2QLJCT/rq32nXDbyNjIL4RG0q\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"mehoqf5t0kvge2myb2gtjqc3\",\n            name: \"<PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998906575666\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$08i7HjSlA1SvrqXoUXczxOseqEF/uyUrA8uS..AMkEt/gY6I7X07G\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher Di<PERSON><PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"cz01dsixxlfrv00j0otqfk45\",\n            name: \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998976959443\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$TANAXaakOHdqLGVc1Gi9IO/kFqfd1u4o5YdUHWQWlKOn6GAtK005y\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"x74ivjvz1ayplcmwrigk1i27\",\n            name: \"<PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998886145014\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$lGHPyU0wK0S.TgXI9Wyv4u5pN.SFXkHT02GhVaV4sdVjzroWRlANa\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"klgpzvp5agdqz4twggdeqcl8\",\n            name: \"<PERSON><PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998973941911\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$7jPesovALQtFbsWl0ZEeNuCBea2SzFjRNJ8.wdjEKInQOq.8qKCwa\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"hu5zwyhqkiv3hufvlu3tfqy6\",\n            name: \"<PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998915571499\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$jHILCvVsvMYRk4NaAqhx3OxUxoPE2udJ8AhbCSJ.voLptyxskX7Rq\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"z154t0tyf4cjpl70d1kssqnt\",\n            name: \"<PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998915524022\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$GMHEsuigWyMvfA7aaP1KJ.QSFzMCeQVsFl.S8l74aT2cQjnzNZAVy\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher Far<PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"suuvguq0uorytk970tcy3r6g\",\n            name: \"<PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998979313993\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$3w2scrOJY80sXM.kYj9fcOVBJfrUJxAqJxFuaX2QZwEuJoly2xQ3e\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"c3a8vlub3uq0s36oiakofwzj\",\n            name: \"<PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998915499179\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$yhjRHWhSYH8jAKIkoPlNuuwCNIPkUPWCrEUiP9SvGByOcvkZnTK1C\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"mrs3hazybgce71a3in7jkn9v\",\n            name: \"<PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998885789208\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$y2OyzxCovID8UmFpfYAokeP.j0iu5pnQGZj56wDyS/7jzFpR5SqyS\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"z02ieo9ip9hgraa04tfnsemy\",\n            name: \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998902126708\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$aobhJsvxOUQA4yIH/4Bnh.paw2nM/zy3NxGXp.PBTpm5PIm.Ti7B2\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"ospiyzmpluqjat97d0e8t6tu\",\n            name: \"<PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998906000425\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$JwuJDl64F/mN4aat0b0C5.Z8opLbUBnfpH/NOYgN6XrkhY0IJYdsy\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"ri0tgm5v7ieqppa98ep3x7ht\",\n            name: \"<PERSON><PERSON><PERSON>ov\",\n            email: \"<EMAIL>\",\n            phone: \"+998979340001\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$jGSrqWH/vrhnipPRcfUQ4OdEJvKGMEtkVIDFsjEsn1pNgouGWb/M2\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher Shav<PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"uppqkhizkzy14x92ueq6wnho\",\n            name: \"<PERSON><PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998902706944\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$n/fvs9Ax.S3.X81ad2uQM.LCPTtV6BD8Vwkc.HwWfBCtUuNCVyU8m\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher Di<PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"nbteuejdxiowgnnosj5jw83n\",\n            name: \"<PERSON><PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998955203006\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$Z9U9ThghQ7JztBJ5Kn/lv.imrDoRrLCklurauOxYEvrLDuvoXBcaa\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"etc1aaqrcvkxg0xygbo7esvs\",\n            name: \"<PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998913177799\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$cvgIswaM/8UZw0GdADeiYODvGhM/HTNybXvMS56i8JW8VdLB9phJi\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"f4uaodgj4o5kvt4u4723glwc\",\n            name: \"<PERSON><PERSON><PERSON>varo<PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998957902032\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$0nrXGeyyPXCVcC/ebT5u2O.1No5IMuktPLxU3ifhdPQPxbVNnGXsm\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"jadg3odeji6twbzjuwkyta8c\",\n            name: \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n            email: \"teacher.muk<PERSON><PERSON>-<PERSON><EMAIL>\",\n            phone: \"+998904733433\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$VM9Qie9Bg8WimUfnCrrpj.R3HafGfTZc3IfV8QKPGU0PUuziQY7NG\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"jxdbcf7gm6p981z0h5staxlo\",\n            name: \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998889114474\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$3tLjTGR4PExxx4sXkLLK6e.ZNsN.GfToIlgpCyG8.IlP52.ZO.g6C\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher R<PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"olhk942lhfl63r5l9rqjxixw\",\n            name: \"<PERSON><PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998902851557\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$BoCGoT7uyBCESP1LNFjQt.T0FL.DO6lHXUZEWPwIAutw6maCk2i7O\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON> : \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"khythwdjapxtwj1bkephbqlh\",\n            name: \"<PERSON> \",\n            email: \"<EMAIL>\",\n            phone: \"+998901000001\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$CJqwQpjwx6KNd3lwryXAXOh3Fbk60MY5iN3jXiaiIBacVuz92/H/G\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"bozt5npc0uxpc7ykfj1f6tva\",\n            name: \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998973986910\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$T0y1ezUHZD.3gklsrI6XKeLDG7K8xjMhexVfTqdlXfhzr8rKABRoS\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"xiat4k4q1zhe53wwnq1ex6m0\",\n            name: \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ltayev\",\n            email: \"teacher.sho<PERSON><PERSON><PERSON><PERSON><PERSON>@innovativecentre.uz\",\n            phone: \"+998887075222\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$RBDBDnLthNsBRlgtpKQ76ur8e4eO3WspNqDEcN6cT3MNkJ6YiWPp.\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"fcw73713xd31yb803ecvszyc\",\n            name: \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998889226169\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$M73L/4BRGyB4dFSYKoic3.giOXvKbLk0rFKKIP4lCgsilQbSNwEO2\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"a5qi30ui9cvfln6dkc438zz0\",\n            name: \"<PERSON><PERSON><PERSON>urodo<PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998946626970\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$IjZfWs666XHuocmV55e6seCTBHBjsvciUUJf9vmd1DtfQeVS/p8ia\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"se8h4gslnqzml23fz7ef8urt\",\n            name: \"<PERSON><PERSON><PERSON><PERSON><PERSON> Tursunova\",\n            email: \"<EMAIL>\",\n            phone: \"+998996202110\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$FcXJvJWof2J30nLsTT.kCOjVuGK4SZonjiOOFg9l4eC.TwJ7IR0HC\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"ycdqawknfrkb9ur17xzxacgl\",\n            name: \"<PERSON><PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998901000002\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$hZiTQBINW4WIg7FrZIMIquTNe1CR1PwOsAfpStQMn8EUKcp1p3wAu\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"kpup9646h1bugqvmnaxldf6l\",\n            name: \"<PERSON><PERSON>\",\n            email: \"malik<PERSON>@normuratova\",\n            phone: \"+998901000003\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$QmXgXm6rcwS22cpbH/3ZFem/9UiLbRggysaVA/tyk12f/Q.6ajdiO\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher  <PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"co9u1yqm899aeqid0gbx61w7\",\n            name: \" <PERSON><PERSON>\",\n            email: \"<EMAIL>\",\n            phone: \"+998973900522\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$E46pCbs3OYXfHM2AUw5Nre7eU2Nzw1FLdDpUxPGmDV52wr0ZEfNYK\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON>: \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"symspae5k1v0np4uhy6hkyqd\",\n            name: \"<PERSON><PERSON><PERSON>\",\n            email: \"amin@26\",\n            phone: \"+998901000004\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$s9MXHeqtOOp96ev85.Xy9OQKVIbdzpmCH8e/HE2uM3BzumBUAfjSC\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Teacher <PERSON><PERSON><PERSON> : \nInvalid `prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:201:27\n\n  198 \n  199 if (!migration.dryRun) {\n  200   // Create user first, then teacher profile\n→ 201   await prisma.user.create({\n          data: {\n            id: \"c98lba3xdysat0d4ok0sbcgv\",\n            name: \"<PERSON><PERSON><PERSON> \",\n            email: \"<EMAIL>\",\n            phone: \"+998901000005\",\n            role: \"TEACHER\",\n                  ~~~~~~~~~\n            branch: \"main\",\n            password: \"$2a$10$ExF/40qBeuh2N9vExNwm2uobPH7XkABslAn8tYMqQkF/xRezcJQvy\"\n          }\n        })\n\nInvalid value for argument `role`. Expected Role.", "Course A2-English-448000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create(\nUnique constraint failed on the fields: (`name`)", "Course A1-English-387000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create(\nUnique constraint failed on the fields: (`name`)", "Course Individual-English-1474000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create({\n          data: {\n            id: \"xamy6355wga5ynyu2d73i6pb\",\n            name: \"Individual English Lessons\",\n            level: \"Individual\",\n                   ~~~~~~~~~~~~\n            description: \"Personalized one-on-one lessons tailored to student needs\",\n            duration: 4,\n            price: 1474000,\n            isActive: true\n          }\n        })\n\nInvalid value for argument `level`. Expected Level.", "Course Speaking-English-507000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create({\n          data: {\n            id: \"rhl3etd7dlgfhladutio4gqy\",\n            name: \"English Speaking Course\",\n            level: \"Speaking\",\n                   ~~~~~~~~~~\n            description: \"Focused course to improve speaking and conversation skills\",\n            duration: 6,\n            price: 507000,\n            isActive: true\n          }\n        })\n\nInvalid value for argument `level`. Expected Level.", "Course Kids-English-324000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create({\n          data: {\n            id: \"q49bq13x3ozrhndwjdineyzh\",\n            name: \"English for Kids\",\n            level: \"Kids\",\n                   ~~~~~~\n            description: \"Fun and engaging course designed specifically for children\",\n            duration: 20,\n            price: 324000,\n            isActive: true\n          }\n        })\n\nInvalid value for argument `level`. Expected Level.", "Course IELTS-English-586000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create(\nUnique constraint failed on the fields: (`name`)", "Course A2-English-387000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create(\nUnique constraint failed on the fields: (`name`)", "Course B1-English-527000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create(\nUnique constraint failed on the fields: (`name`)", "Course Math-English-422000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create({\n          data: {\n            id: \"n85g4352aunnlarufjdunop1\",\n            name: \"General English Math\",\n            level: \"Math\",\n                   ~~~~~~\n            description: \"Mathematics course covering essential concepts and problem-solving\",\n            duration: 12,\n            price: 422000,\n            isActive: true\n          }\n        })\n\nInvalid value for argument `level`. Expected Level.", "Course Math-English-492000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create({\n          data: {\n            id: \"ger3lh2d7pdp171qnshnj9z2\",\n            name: \"General English Math\",\n            level: \"Math\",\n                   ~~~~~~\n            description: \"Mathematics course covering essential concepts and problem-solving\",\n            duration: 12,\n            price: 492000,\n            isActive: true\n          }\n        })\n\nInvalid value for argument `level`. Expected Level.", "Course A2-English-448: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create(\nUnique constraint failed on the fields: (`name`)", "Course B2-English-487000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create(\nUnique constraint failed on the fields: (`name`)", "Group B1+ new  FULL: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 new green (16.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 new (07.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 green new FULL: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 green (17.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A2 new (08.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B2 new (05.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 new (02.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 new (7.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A1 new (16.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 new (23.05): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 new (02.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A2 new (02.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 new (05.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A2 new (19.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 new (19.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 new (05.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 new (15.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 middle (orange): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 green new (2.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A2 new (2.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 green (04.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 module 3: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 new orange (1.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 - pre IELTS new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 pre-ielts new (1.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 new 11.06 green book : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 middle: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 new module 2: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A1 new (10.06) FULL: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 pre-inter: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A1 new (09.06)Full: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 pre-inter module 6: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 new (22.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group IELTS individual: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group SAT engl new (3.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group SAT engl MIDDLE: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group Speaking: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B2 new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A1 new module 2: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group KIDS (full): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 module 9: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group KIDS module 4 (full): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new module 2: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS new 16.06: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1  module 3 : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B2 middle : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group ielts new 03.07: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS new 03.07: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS  30.06 NEW!: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 middle: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A2 new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 module 3 : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group SAT English : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 module 3: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group SAT English : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group SAT math Intensive FULL: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group SAT math intensive (11.07) 2.5: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 new  (23.06) : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A1  New 16.06: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2  : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2  new (14.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2 new (15.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new(7.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2 new 4.07: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 middle: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group iELTS new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 new (02.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group IELTS full: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new (03.07) full : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 +Blue book new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 +Blue book new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B2  : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A2 middle: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 pre-inter (18.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A1 (module11): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 (module 6): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group Individual: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 middle (15.05): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 (15.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1+ new  (01.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group IELTS(middle): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new(12.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS new(3.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B2CEFR: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 pre-inter new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2(middle): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A1 new (nabor): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new (24.06)Full: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 green (1.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 new purpule 07.07: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A1 new (4.06) 4 module : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B2 pre-ielts: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 purpule new 8.07: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 new green 1.07: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A2 new (03.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2 new (09.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 new (9.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 new (11.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A1 new (18.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1(2.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2 middle: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 green new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group IELTS new (26.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 end : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 purple (5.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A2 new (nabor): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2(new): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B2 new (nabor)4 mod: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 new 02.06 orange 3 module : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 new (nabor): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group IELTS New: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS NEW (20.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B2 middle: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group IELTS Intensive: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B2 NEW: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group IELTS new FULL: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS new : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 new (3.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A1 new (nabor): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 new 16.06 : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group IELTS NEW 16.06: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B2 NEW 16.06 module 2 : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 new (4.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group IELTS NEW (8.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 new 12.06: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group IELTS: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 new (10.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 - pre IELTS middle: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A1 new (4 module): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B2 - pre IELTS new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group Math beginer  (5 klass ): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new (full): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS NEW : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS middle (Full): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS  middle: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new (full): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group Ielts  new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new 27.06: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2 end 11 students : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1  4 module : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2  4 module : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group Math abiturient : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group Math 7 class : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group Math 6 class : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group Math 8 class: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 full: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group IELTS new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS new (1.07) FULL: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2(end): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2 (4.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1(middle): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group SAT Math 2.07: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A2 new 10.07: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new(10.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2(middle): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1(middle): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1( inter purple) 7 mod: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B1 green(mod 6): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group IELTS new (07.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B2 new (14.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A2 new 14.07: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group INDIVID: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2 new nabor: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group Kids new (nabor): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group Kids new t: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new (8.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B2 new (4.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 new 4.07: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group B2 new 04.07: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A1 new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS (8.07)Full: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS (Full): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new(16.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group IELTS Intensive: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 green new (14.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group A1 new (12.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new 18.07 : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2 NEW (24.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group C1 : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 new green (17.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_teacherId_fkey (index)`", "Group Math 5 klass (17.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group Kids  new 22.07 : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 new  22.07: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`"], "mappings": {"cabinets": {"cm7n4pe08000110hm4tc4ocew": "uzn1f8lrkk9c2l2l8szp7smn", "cm7n4qdna00014r6zt9pqebgo": "k7dtcjgc7rrv3cqjw2my7x4m", "cm7n4rmbc00034r6zcjbiadzu": "ff82b2j504bewlsdng9jidf0", "cm7n4v5uy00014x8i7p7z3318": "g2hau0ay29tpcn0c9blgkuca", "cm7n4yyt500064r6zae6ui4ag": "xmrz4tni46k3ll2v9mjt7ynf", "cm7n50d7b00084r6zcl1ghtnc": "umvuix3itsvle4ghurs6fsmb", "cm7n5191000024x8i3kfl2n9y": "xk43sxmypcapgumguui4enu2", "cm7n52jog00034x8il03s9lhe": "dj8e00al6k2mtk6dmnk93zec", "cm7n55cgz0000123zavxf7jcu": "ywr56kmddlbw4ltfq4be5ki5", "cm7n56d330001123zryfchkqy": "pmhz5t2ttk1v7f3k3z3ela9l", "cm7n56tee0003123z35ylpjrr": "v6j3taktbssc9rjrwijtouq3", "cm7n570m30004123zgcqo24iz": "kdhal3xin894fp03ljn2jbbe", "cm7n575ts0005123zmlybgbyk": "otmil3k8lfzh0yhsp4xlxelp", "cm7n57b3i0006123zr531s3h9": "x84lk8m41pjk7vdfzzby437l", "cm7n57rq50007123zsbl09zue": "trjjantwmme2zbmj25gwj256", "cm7n5977i0008123zrxl0uyaw": "q8m5qagq4wi50enpk0wsp6wu", "cm7n59ni70009123zbky7948o": "ilyydx1aqlozljxii7wfsafz", "cm7n5j1gx00011411foknb0ly": "zm1poaf9yzbap5rpknjcwn16", "cm7n5jk7r00021411szw1mw09": "ms8qu55z3fgtuxi534q6yeda", "cmc36mqht0008ngu3v1ct4i07": "lmmy8onafsgb3snak1fwn1n1", "cmc3ak3kt0004a918cm045ze0": "ren9fblhsf3e9rd9nhuhtpec", "cmc97tbh100029c9i71fqjotm": "rboyr249dza1179z0dbp3qhg"}, "teachers": {"cmc8mk7or0000dk5earhataio": "p1lm6erxwubfg7osnqi7napp", "cmc8mrxug0000yipubq47xapy": "k5rbsxqkgqpkjo8y4msyxdvc", "cmc8mwzuh0001yipuo462sp47": "r1ikbxkmzdq0qyg9e9wowbrm", "cmc8mxm4y0002yipupix86g63": "egmou5neiz06tuofdmqgwcn8", "cmc8my6n60003yipu0uf12nhg": "m80f7hasazcykl5aefbj8tfl", "cmc8mzcrs0004yipud37mpo15": "q5y6bopr9d00ifoz2u0cnt45", "cmc8n0zvr0006yiputvovsmy6": "whuqxuc2cvxfe8lnnzhxqs8l", "cmc8n1nv20007yipusgazu9pu": "rtl96s4nf2eg2kgea0uae9qs", "cmc8n28fw0008yipuzek85hdt": "iqk7hvj9dzc8t8seds6pomxo", "cmc8n2u4i0009yipu9k5qpxhg": "ft3zkie6udcdcvlnut34qfsm", "cmc8nmrkz0000hj8g13k2r49c": "wlk4l8ks4e2jgxmeevzjhgua", "cmc8no0i00001hj8gc4n7xdvp": "d8gcp0h15xze3wzrgk6li513", "cmc8nwq5s0000lplge4a7tt9w": "wm289lxg821ixdquzai6iza0", "cmc8nxr9o0001lplghse3n50b": "tlb7rhjhxuqa55vaescycagi", "cmc8nynca0002lplgeeh6m1tn": "o2kwjx5sogbcbl5mbiw2g8pz", "cmc8o0iu40003lplgbc5s922e": "ch8mqittk1jww6ja2wxifckn", "cmc8o1b0k0004lplg4yybh8dh": "epg2kgfelz5bkipq0jwxkoam", "cmc8onzmr0000jp5bn8gzz7by": "ooeo7qynrzeyekw68cxb7wyi", "cmc8ooujp0001jp5besanhx2s": "tb98qg31xi5ezpcgt4a9lond", "cmc8opz7z0002jp5bdxr660ui": "phr454iwv2jtmtscowlpzcqi", "cmc8oqwwx0003jp5b44rl2x3j": "x7yzcr7y880gr53luzrtkok2", "cmc8wzf2v0001145qgfzj21w3": "f5mcoyl5g9zcus9jnvohyp4b", "cmc8xqrfx0007m55q6sqrb5nq": "kwbddid182e8u0p4dp2ow0cn", "cmc93qvxo00063tz69dkfnz1n": "gm2btc881qqbc1vmnm8rm4ak", "cmca0c12g00034irplr696163": "pz2hm4rqxi4qzt2eoqdyb4g9", "cmca0hojg00054irpeqlp24xc": "zny1me1lduc1kvo8gm2ahml2", "cmca1zhfg000011z0akg9c57c": "aen53dzqc9iv2zhd8hg8dktv", "cmca3yga00005ypbirkl6o7y8": "oirnowzp49gek2yge7vdf88t", "cmca4w4g80002wsb3qjkv4vfo": "woliajjj5ydpj9e6iwhtglrr", "cmca5hduu0007wsb3li5w4jf6": "t7lgn1fkmijnnako09abmr5h", "cmcd9fc010003mva86b3lnmom": "c2bdtu5uixjtta746hpnn9vx", "cmcel210q0000jcppf0o416va": "v5kuhir5xg4ya9pd4xx8rhf3", "cmclio2ju0001ked72a90t0en": "c53sd22uayct1f4oi64yqkol", "cmd084odk00009pgfeb87pcm6": "wa34nqwx0dpw7488z2h7jps6"}, "users": {"user_cmc8mk7or0000dk5earhataio": "oo98xo4ri28uphktpxxtvw52", "p1lm6erxwubfg7osnqi7napp": "oo98xo4ri28uphktpxxtvw52", "user_cmc8mrxug0000yipubq47xapy": "njkohbijgzjd2zr68i9ootxe", "k5rbsxqkgqpkjo8y4msyxdvc": "njkohbijgzjd2zr68i9ootxe", "user_cmc8mwzuh0001yipuo462sp47": "sfzaeem49j7svpen6iv32upk", "r1ikbxkmzdq0qyg9e9wowbrm": "sfzaeem49j7svpen6iv32upk", "user_cmc8mxm4y0002yipupix86g63": "ynitl0j52n3o9x5iulxd2r0x", "egmou5neiz06tuofdmqgwcn8": "ynitl0j52n3o9x5iulxd2r0x", "user_cmc8my6n60003yipu0uf12nhg": "mehoqf5t0kvge2myb2gtjqc3", "m80f7hasazcykl5aefbj8tfl": "mehoqf5t0kvge2myb2gtjqc3", "user_cmc8mzcrs0004yipud37mpo15": "cz01dsixxlfrv00j0otqfk45", "q5y6bopr9d00ifoz2u0cnt45": "cz01dsixxlfrv00j0otqfk45", "user_cmc8n0zvr0006yiputvovsmy6": "x74ivjvz1ayplcmwrigk1i27", "whuqxuc2cvxfe8lnnzhxqs8l": "x74ivjvz1ayplcmwrigk1i27", "user_cmc8n1nv20007yipusgazu9pu": "klgpzvp5agdqz4twggdeqcl8", "rtl96s4nf2eg2kgea0uae9qs": "klgpzvp5agdqz4twggdeqcl8", "user_cmc8n28fw0008yipuzek85hdt": "hu5zwyhqkiv3hufvlu3tfqy6", "iqk7hvj9dzc8t8seds6pomxo": "hu5zwyhqkiv3hufvlu3tfqy6", "user_cmc8n2u4i0009yipu9k5qpxhg": "z154t0tyf4cjpl70d1kssqnt", "ft3zkie6udcdcvlnut34qfsm": "z154t0tyf4cjpl70d1kssqnt", "user_cmc8nmrkz0000hj8g13k2r49c": "suuvguq0uorytk970tcy3r6g", "wlk4l8ks4e2jgxmeevzjhgua": "suuvguq0uorytk970tcy3r6g", "user_cmc8no0i00001hj8gc4n7xdvp": "c3a8vlub3uq0s36oiakofwzj", "d8gcp0h15xze3wzrgk6li513": "c3a8vlub3uq0s36oiakofwzj", "user_cmc8nwq5s0000lplge4a7tt9w": "mrs3hazybgce71a3in7jkn9v", "wm289lxg821ixdquzai6iza0": "mrs3hazybgce71a3in7jkn9v", "user_cmc8nxr9o0001lplghse3n50b": "z02ieo9ip9hgraa04tfnsemy", "tlb7rhjhxuqa55vaescycagi": "z02ieo9ip9hgraa04tfnsemy", "user_cmc8nynca0002lplgeeh6m1tn": "ospiyzmpluqjat97d0e8t6tu", "o2kwjx5sogbcbl5mbiw2g8pz": "ospiyzmpluqjat97d0e8t6tu", "user_cmc8o0iu40003lplgbc5s922e": "ri0tgm5v7ieqppa98ep3x7ht", "ch8mqittk1jww6ja2wxifckn": "ri0tgm5v7ieqppa98ep3x7ht", "user_cmc8o1b0k0004lplg4yybh8dh": "uppqkhizkzy14x92ueq6wnho", "epg2kgfelz5bkipq0jwxkoam": "uppqkhizkzy14x92ueq6wnho", "user_cmc8onzmr0000jp5bn8gzz7by": "nbteuejdxiowgnnosj5jw83n", "ooeo7qynrzeyekw68cxb7wyi": "nbteuejdxiowgnnosj5jw83n", "user_cmc8ooujp0001jp5besanhx2s": "etc1aaqrcvkxg0xygbo7esvs", "tb98qg31xi5ezpcgt4a9lond": "etc1aaqrcvkxg0xygbo7esvs", "user_cmc8opz7z0002jp5bdxr660ui": "f4uaodgj4o5kvt4u4723glwc", "phr454iwv2jtmtscowlpzcqi": "f4uaodgj4o5kvt4u4723glwc", "user_cmc8oqwwx0003jp5b44rl2x3j": "jadg3odeji6twbzjuwkyta8c", "x7yzcr7y880gr53luzrtkok2": "jadg3odeji6twbzjuwkyta8c", "user_cmc8wzf2v0001145qgfzj21w3": "jxdbcf7gm6p981z0h5staxlo", "f5mcoyl5g9zcus9jnvohyp4b": "jxdbcf7gm6p981z0h5staxlo", "user_cmc8xqrfx0007m55q6sqrb5nq": "olhk942lhfl63r5l9rqjxixw", "kwbddid182e8u0p4dp2ow0cn": "olhk942lhfl63r5l9rqjxixw", "user_cmc93qvxo00063tz69dkfnz1n": "khythwdjapxtwj1bkephbqlh", "gm2btc881qqbc1vmnm8rm4ak": "khythwdjapxtwj1bkephbqlh", "user_cmca0c12g00034irplr696163": "bozt5npc0uxpc7ykfj1f6tva", "pz2hm4rqxi4qzt2eoqdyb4g9": "bozt5npc0uxpc7ykfj1f6tva", "user_cmca0hojg00054irpeqlp24xc": "xiat4k4q1zhe53wwnq1ex6m0", "zny1me1lduc1kvo8gm2ahml2": "xiat4k4q1zhe53wwnq1ex6m0", "user_cmca1zhfg000011z0akg9c57c": "fcw73713xd31yb803ecvszyc", "aen53dzqc9iv2zhd8hg8dktv": "fcw73713xd31yb803ecvszyc", "user_cmca3yga00005ypbirkl6o7y8": "a5qi30ui9cvfln6dkc438zz0", "oirnowzp49gek2yge7vdf88t": "a5qi30ui9cvfln6dkc438zz0", "user_cmca4w4g80002wsb3qjkv4vfo": "se8h4gslnqzml23fz7ef8urt", "woliajjj5ydpj9e6iwhtglrr": "se8h4gslnqzml23fz7ef8urt", "user_cmca5hduu0007wsb3li5w4jf6": "ycdqawknfrkb9ur17xzxacgl", "t7lgn1fkmijnnako09abmr5h": "ycdqawknfrkb9ur17xzxacgl", "user_cmcd9fc010003mva86b3lnmom": "kpup9646h1bugqvmnaxldf6l", "c2bdtu5uixjtta746hpnn9vx": "kpup9646h1bugqvmnaxldf6l", "user_cmcel210q0000jcppf0o416va": "co9u1yqm899aeqid0gbx61w7", "v5kuhir5xg4ya9pd4xx8rhf3": "co9u1yqm899aeqid0gbx61w7", "user_cmclio2ju0001ked72a90t0en": "symspae5k1v0np4uhy6hkyqd", "c53sd22uayct1f4oi64yqkol": "symspae5k1v0np4uhy6hkyqd", "user_cmd084odk00009pgfeb87pcm6": "c98lba3xdysat0d4ok0sbcgv", "wa34nqwx0dpw7488z2h7jps6": "c98lba3xdysat0d4ok0sbcgv"}, "courses": {"B1-English-494000": "bd5spxbzlbof5uist64rzv08", "B2-English-527000": "qbqca2jjq6ih1zfdbzforwj5", "A2-English-448000": "f71mig0h9b9c42zlei3yom7e", "A1-English-387000": "yvf4337dxdsb1sam3svmuo39", "Individual-English-1474000": "xamy6355wga5ynyu2d73i6pb", "SAT-English-568000": "v5no2eli9q1sllh0przxs33b", "Speaking-English-507000": "rhl3etd7dlgfhladutio4gqy", "Kids-English-324000": "q49bq13x3ozrhndwjdineyzh", "IELTS-English-586000": "ao6mnp266guvfl3vz7gpi127", "A2-English-387000": "rw3j6qnsr0te7scb3gvxv46t", "B1-English-527000": "bwwbh2z24ignogwvzy3zoii6", "Math-English-422000": "n85g4352aunnlarufjdunop1", "Math-English-492000": "ger3lh2d7pdp171qnshnj9z2", "A2-English-448": "a0d88en5pei9s67pnz45nir7", "B2-English-487000": "cgrqxtafnpll6tme1rvw61c2"}, "groups": {"cmc8owvo30000dnwiqsxsjoo1": "rnlnkolln7h5hb7p34bqdesw", "cmc8oz3tx0001dnwivzgup0ty": "ebow8wzat0jwa2bjbns8lcuf", "cmc8p3vpb0004jp5bin1nfj8l": "ur50dhgs08odap0ji3m17oj4", "cmc8p53nw0002dnwiaqasonli": "tejv6tydg9mna3zoo0817z0j", "cmc8p7ys60005jp5ber9vytsz": "n72x5h4jfe716lqim591h5hf", "cmc8p9dhp0006jp5bs65ezopv": "finyqrkfmdmy8ibfz2ro4j53", "cmc8pbpl80003dnwipzso68wq": "ssc4hgrpiahua33l7w0z6ras", "cmc8pg4rf0007jp5bwcn1pap5": "ad6lz6rctojw5qf78c1vwxr7", "cmc8ph5hm0004dnwitdzh2qct": "ek5t9y2f47y6zw18jexss7yx", "cmc8pinr40005dnwismmwxieg": "i5zxtprrpi5zhqi4czptgfz4", "cmc8pkg3i0006dnwi42o2vro9": "wqbhwbk79xoab296oun2g2i6", "cmc8pqyq10007dnwihf6qdz9m": "f03gum0uv83ckm1zkei05rmj", "cmc8pybf80009jp5bsd06mksa": "eb9xi0fh5i3ojgh4k6wytq3t", "cmc8q6k1q0008dnwill3wkv1h": "q2t5nc4xxrapzua2vbkjaef7", "cmc8q7mb90000mx4jrqlo5p5f": "bxovaadzmbvl9qroyif2pbvz", "cmc8q9wfo0009dnwio1nao7nj": "remoty15mbgob6gs1vtt3f3i", "cmc8qaqof000adnwihl1ap021": "xug6yloa22j0i56g3vsga28g", "cmc8qbibs0001mx4jv05jzaos": "q58bpvklxf8pt107vyycael9", "cmc8qcdq60002mx4j1h5pwmmm": "egtnd09e1ytxvauz239s09fj", "cmc8r3w2e0005mx4jnypzqq9u": "ahkecqld93lx417fxdcof1z0", "cmc8r8j610002oc94g8gef210": "oolj3owbr8gkh0my9tbaze2b", "cmc8rfcqp0003oc94u0v3g8du": "qhjl1v8b3n6mnfbymhnuroy3", "cmc8rqi5k0000taw1jb0bmxou": "lrpc2wcz4yvosqvrxa75x0zj", "cmc8rs75n0006mx4jqb04yncw": "qhjuvzzabtv4xpmzcquihqi6", "cmc8rtop60001taw1ukriqotz": "fbr4sn99er0qx9t5gacldmqr", "cmc8rwv300007mx4jpb2t40x3": "cllmaobrnjxihk4nq6r0ydbm", "cmc8t8tgf0000iu11yeu8f93j": "khw68b1gfxiaqw5gmv4w5ipy", "cmc8t9qku0001iu11en1m8mll": "j54hjhffabhnnes4jlp44jrn", "cmc8tc5kk0000zrce6u2cn05r": "eumyewti7och5xngwtsitsr4", "cmc8tdrbq0001zrcep0i0rs0w": "t4u0f61sa9am2ifcha1dmvsk", "cmc8wchg40000no9uvxo4o659": "nmqac6mv8v9at9d5kpquq5w4", "cmc8weuuh0001no9u31vbxjd2": "ahmjci47d0vyifpu8l4ljd5c", "cmc8wlczz0004no9upvc01b0t": "tenvng88os3ofmssg95g81ye", "cmc8wmkx20000owpvey6wqpni": "c4fwabwje9du0gy6g2rnoqgm", "cmc8wsmq80006no9u1px001q0": "hp7rz6z8expg6ju8ohdmanqu", "cmc8x4hhc0001ueawv9e9awnu": "ru3kg6gzxmnsv6imz61cuoog", "cmc8x6uw10002m55qeulkal8b": "b847csf7euz4zf2a41ee2ies", "cmc8xag8w0003m55qf58aqbwh": "f381r05fra4qzi13zlpg5t58", "cmc8xbhiy0002ueawqd3dtqfc": "lgt3na5ol4b9mfkujhmceey0", "cmc8xc6oe0004m55q4oysuqlj": "fdly6d0yp73i2uxir0q8w7iu", "cmc8xsyqa0003ueaw9hcjo72h": "bgadfoyiqqa7fwlvcj9jbw36", "cmc8y3nci0000sm3k12xzh97f": "ddvkqjdplytbmzq6pzuqhm4u", "cmc8y5lnr0008m55q8fjs6s6d": "j8veqjad85oitcbsyyhmukow", "cmc8y79ic00002hcc9crvsvg9": "xc8xkhpn8bjnjdzfq4sjprqy", "cmc8y81zr0006ueaw9ni9s1ts": "rc5as0y7d5wup99m436077eb", "cmc8y9a680007ueawsuerhcxs": "denfykjf9awlh0gic8nmxbvg", "cmc8zq7io0000hyiz0zljgth4": "flzwr366e7l38xwjtluz56c2", "cmc90noiv0000sjfcrl7s7m5n": "g5ykdxo88uho5wupawxyf7n2", "cmc91btea00013tz6gfkgu19u": "h517npnr2d8kc64eftrim7go", "cmc91s9ol0000oakip4mcfj7s": "tepjo7m9ge312jspainr819b", "cmc92jafe0000w4x0ar7nfo2l": "di1vkrotkgshn5rh65bx3hhy", "cmc92mmyx0000s63ic65p2s0b": "e46ia6k8z0ku40306v79j3k3", "cmc92r51d0000g74iqp7de02k": "gtzw1uvc3tcw8x4xy2bw1tr1", "cmc9358rl00023tz657ab0p5k": "grhaa6x2f8h3cblb9hgxns7i", "cmc937yzc0001vwkay1nhs85g": "e1sqnkx71h36668oo039bbu1", "cmc93azpd0000yhhp1y300v0y": "ughpbrvjqh2chu74i5lzylnv", "cmc93b1ol0004vwka4qiwm2rr": "la87k68n9elmpaoaqb6voce8", "cmc93jq6b0002i0tnxmjvbdz3": "l7aiyh3b113vcyvooibi58bt", "cmc93vtly00073tz6gyitbmwq": "zs44gwbbujunuv1p4lq5kvfb", "cmc93yccq00093tz61mkycgx4": "gycwvbjcaz32hhdtkgrc8bkk", "cmc94ex580001arf9ohjf40xw": "lscpxsuw1p547qsjbc4vvhz9", "cmc94it6v00002i1lm9c336xb": "x6x6uos39qd231shk6080f1c", "cmc95v3c100032i1lq2ge4igk": "sk5e5v9qxct8k46s4szk600p", "cmc96zmne0000tx83cina54tc": "suzuqnhatnhwkn844vuyi2rq", "cmc985iyw0003121sz4re1xe1": "gz89yjfmdy2ykgsvsftchgl4", "cmc98as4a0004121siuoh6oe4": "rvomib15eyqv5mqglc3encmi", "cmc98cqg20000bfnz5r2oqsjp": "odvp71m3mepkrzt4u83uo4tv", "cmc98fj7z0005121s4hhz8x5h": "n46mvk1yhhazih2vbwe2rrij", "cmc98jz1p000c2i1lus28fr1m": "og46opvb14yspxpwv47mm62i", "cmc9zsocw00014irp9310x2b1": "y75vplse3429nxn5u7u71bhy", "cmc9zupqt00024irp0rr8vxll": "xsspceyvuqjq82libcav4uqb", "cmca0eee700044irp6lf2a9xk": "z55n311i3mbmdc7wnvjyqwn9", "cmca0ks9n0003ycmm0cxxxdpm": "h5les7y3vhpij6xfrwiryp6c", "cmca1dxyc000017ijosru3ptl": "ojzpncie473yop62vv8iblm4", "cmca1vc9e0000527fitysfxxj": "avu04gnjo03si7sl02oppw9r", "cmca1wmhh0001527f61yg3n8q": "s3eggplscaa9ik1utjaj88dn", "cmca1xfkm0002527fm0eh00jw": "uf8hin7zvo6mmgphlcsuepyp", "cmca23j7i0003527fvlmfx617": "d5cia11onzj12l9qbipyi93c", "cmca24pvc000111z0wcml1zpq": "cjp59e2ygxg76i16dzr37ucc", "cmca2nj0l000411z0lltyshy8": "z9mv2jcxkky67qzdyibf1sfa", "cmca2sqli000511z0znuork20": "j899qqa0cksol8yj4g87sj9g", "cmca2ywhl0000ypbidqep2e1w": "arq69i418kdyelnopv70u0db", "cmca344uu0001ypbii7ggrsw9": "bnwivgbwez4eedbimvuj6emm", "cmca35cq2000611z05thpjjzh": "qo38zcjmjllrjwpqjzey55ft", "cmca36dxn0002ypbi5shwd1jf": "kkhoc7v37wmxyxtregwuor35", "cmca3aflm0003ypbi1vcai8vc": "w9j3zwjemdopuqemj9gpqxjr", "cmca3eybb000711z03uf9fw7l": "suy1c4onyy08m4qo049zqgmu", "cmca3p7e8000811z0zhw82stf": "yn6bdx9t7isj9nz7kmn8l74y", "cmca3pb1z000911z0cw5kyivu": "go8q3zizvdo2oi3c6vkwvrs8", "cmca3rb1e0004ypbis44nt0r4": "qjicq46exbjayaxqlvtafbtz", "cmca453ak000b11z0uei57ryt": "gmvf4pbh9aocixkgh1pgsjfm", "cmca4a7ki000c11z0fx7wgfpu": "nznqb1l98vmtosabec1jzo1k", "cmca4fs9r000d11z0mbc716tc": "va2b5acqithdje91cwaid9tg", "cmca4g8uo0007ypbi3bl7hfls": "ecs7a68e3s6e5ytcnmip4clo", "cmca4luhm0008ypbivwxh74xw": "x4twuj6i8rto7sudonhtd77m", "cmca4om5m0000wsb3c6ko5log": "wmyidp0iegu3kftkv5gi3lxb", "cmca4qliq0001wsb34v8fvl5y": "algozvusgqzeh84bgfk7y0q5", "cmca4zdqj000f11z0lt7can8e": "xl1jqoyh7y4vjggg3efo36xy", "cmca50peq0003wsb37pkcox7d": "quzutj6tzvf5xqrow0kvmayr", "cmca53tn7000g11z036j6xtqb": "l0f1ozqemo8ibctum6pkizyk", "cmca5cfq00006wsb3fsws02m6": "dsjth3dycngx212dked97xgg", "cmca5dezw000h11z060sp0tsa": "j03uss9r18plumh4obdszber", "cmca5mv9z0008wsb3k7v7iolj": "eaqrq93aspvx9029za5ldr50", "cmca5w36g000i11z0mebsckuw": "c9ad2vuesxm7nxr8y9m3i3nv", "cmca5zp390009wsb3ns057oi2": "c1flynhp7krp437ga2owaqwe", "cmca634zc000j11z069cxph0b": "zvdzz6tlad8wlztcnp28sqon", "cmca74wkf0002h9ucv0963r03": "utwwd0khboj2py1z9cmredtp", "cmca8z3400000deerdoxbvbng": "pjnf9wvuw2ehrz0qu530qeb8", "cmca9i1440000maug3h0e1apz": "zt6f3ki97fonpk0b8zlpym8v", "cmca9lltx0002deershyskqym": "kqwofyo7pjc9yiru06tau5nb", "cmca9maw40001maugvzyxfzb5": "jpuqp6ya2wzatk5mrohsrjzv", "cmca9xgj20001vbm2g17ad6gw": "yhbo7ucgkupu4kwtfoxzzn7k", "cmca9ywn50002vbm2g1fmk4jy": "j0xvh2owfwkq0zygm93m0fif", "cmcaa0dpi0003vbm2iz3g0xi1": "j9xovqykc7ge6oxrzhiwxa47", "cmcaajs990003deerm1w0aq0x": "o8lai9p91nbz1vs5kddpz4oz", "cmcaalse40007vbm2znpl78a1": "cf04w1r49k28uz33ijwu1z8l", "cmcaaneij0008vbm28naseecr": "rg69hce4yru1pol1fvuw07wc", "cmcaaoaam0003maugpzdy4gco": "ytcp0aslyjsq3fofh7wjukla", "cmcaayv0g0000lf2exqi9bul0": "ckhglyz1gfq9c2xvkma9z4fs", "cmcab81ss0005deerxdcnn3r1": "zkykvn2za745fycye77zswf5", "cmcabd9ep0001lf2eigjgl9eh": "pq0aqlwbl0nxv0msillcy5wk", "cmcabforl0000pcwptbbzg0hd": "mejttkblq9236uz9oacl4th7", "cmcabit0k0006deer2xdm32az": "earkmt0n7fcd2u5pln3xevua", "cmcabs2ti0001pcwpvyv1khfl": "vx836famk70wvhjybro43jqt", "cmcabuqou0003pcwpek65bp0l": "zgyiy3v298ru2pm0ifnyd729", "cmcabyn0x0006maugbjvy2epj": "jns3um9krm95eb5sfja3frxo", "cmcac83vv0007maugdlyykyvl": "imgd3vz5s29k1iq5cey175q1", "cmcacalfm0004pcwpm7gvw7ju": "mlbbt5ce8wkjp9kles24olck", "cmcadnqrg0000lwac19e9ki2g": "pyoda9cf9dzudznvamr7plph", "cmcadwxgv000060odd97psprj": "wluhqb4fth2fdk5qgp2kb2lh", "cmcae0g650001lwac88wn2ehi": "wy7jpp6pbipod90xlwxd7qrt", "cmcaf5m9r0000csf9jaype3mt": "hq8166bkj8kigxszf72a3iil", "cmcahwv7j0000pg8yy0l80i8n": "xav8qr64killg7vxt2by9pa8", "cmcahz61w0001pg8yvtnt40v9": "f1phogxuc11wm68m5v34iz6r", "cmcaibv5g00003g74cqvqxx0a": "mn7a5edkyvr0g6jktecl2pkg", "cmcaicay100023g74uucy4a2t": "o7ev358goqlhecu037jkqb77", "cmcaid0j600033g742pc3uctj": "dl0wmmh8s87lsragxr7vsyf1", "cmcakvcbh0000sugu3gswz9ju": "pmq5djyj386jji37lcl119dm", "cmcbeq80r00006jl2wbxyr65f": "c11ni062sgnnho05ibfk60bl", "cmcbergv10001cleyrmcfc5xz": "tgg237xpxbd3ln7ftlreoaz7", "cmcbesozm0000v4a5hbgs30gb": "le62lx8lcla87jghsb6c4v9s", "cmcbu0qk00000msxzrb3x3vyk": "upt7dx16tswkg0im0xvfe1b6", "cmcbvmk8n00014afuogoixd9w": "l7xsi6acv2jm43kpqygv77yz", "cmcbvok8b00001p5dzo14elze": "x489lqxtrs2h8eypmi4x3679", "cmcbvpj2u000168hlhtwyfhv1": "xyfsbe3efpwipowvapzubqtq", "cmcbvqpup0001msxzzj5k8qpl": "sm4q19sqk6610igdo16j12bm", "cmcbw8ldv00011p5d23pr40ea": "avc27n9ouywmzyi151hks1wz", "cmcbwgl2w000368hlwe7fg83a": "ol68vto8kefy9yybav5lej8s", "cmcbx490w00021p5d5efbgydl": "o2u2jqff0x99b5wuac174uq7", "cmcbxf1320000dbbnfssvs4gc": "mghgocromsxzfz3it4cs6xxx", "cmcbywuhf0001rejg0eke1tgo": "s110umc5apneez83u3usgn7z", "cmcbz0vt5000012dao3zvjhbx": "gyrof5gxvsmxa1n4k4ys5x4c", "cmcc0hk9a0000w1mc8858xmrp": "l6cr35u5z5uar7v5qkfthp6e", "cmcc0igf20002pitzrvfngyg5": "xmpnpbfvvldryfhh7lkv97rx", "cmcc0pjrr0000enrfz68qvp1y": "onvlbsncffxp21rgal8ws1y0", "cmcc0td520001enrf25lha5fu": "gqz7ic2hf6qceaolnaprqouj", "cmcc1bjyb00007xvnz8g94mqe": "vfzjc4wjafo7wrinjcf3rr66", "cmcc1gbm90000zjvcucvg9yo0": "q36gtaln6u9w5kcytnu8b4lu", "cmcc336uv0000148magebrz6q": "e903v5w517fai3ewfmsqp4td", "cmcc36dtl0001148mfjiola3m": "ovko3zdk6lh9pq03rxrm6xcj", "cmcc385j60002ed5w6l6wnqxz": "dl50ntbletzgbsgm39x40w3c", "cmccxuhwf0005ohka3mvbzty6": "lav90akktt5wyetk0t4ui502", "cmcef250r0000ubkzceftz78j": "xjypktqiim70lhw2b5gwoia6", "cmcelaac50001jcppxgk2j1dc": "qja760oo0e4t3lkmylgo6f0t", "cmcelemro0001outto4fyi8qq": "ldmhwh1wiytifvwrh2h3xfxt", "cmcelh22j0002jcpp66cjqrt8": "zrvfa194gawntrr0fd9ncono", "cmceowskk0000s3q2pmttd9nr": "g64g8206dfmzc7fh170va5jf", "cmcew3owk00006dgfxjdxsdes": "neg9cjm8cdoexkjx0loqirbr", "cmcfs3rgj00001467arl83dc3": "n57vrkttj07kw673zhn8rjua", "cmcfsgv5v000012wn88m5yojy": "favpjjgdgof0rros7puxia0f", "cmcftfbyj000212wnw998pcij": "fb3rtwa5nl93qx973xgddbe9", "cmcfupk8100002nes3zhrrupy": "id7dt5lr0h6q4wqb8kigkdl0", "cmcfut6up00012nesj7o2k4jt": "ajgmhhf42gr91nazufpe780v", "cmcisxrfo0000lro80il77cwo": "pwt2pqcy8f26o7mjtty5mb3u", "cmcit17xo0001lro8ljyvvx5k": "da5c6i47fwsezlp5aqmvgeyw", "cmcj41xg10002uw6rqdo5487l": "e3cc0rrh6meqvbi3yks358aj", "cmck46ogl000014j6f6tjlksb": "qrji4531a8mjx9ch1jz9zqmp", "cmclj29bd0000kk95mp22u1ad": "dgv71mc23pvfh4eiohe1p4zo", "cmcljxmmc0004vlyko6bk6jtp": "cwpon8cjg8jub7hrod0fodvu", "cmcll7yuo0000oynqigt893f5": "n2mivz156hff616wxpa3ruia", "cmcob5a4q000196w6kshj21ju": "kpedl7m7yfmzdc9cll7ts56w", "cmcoc6wgn0000i6yfez1h4uts": "thwkev2jucsjt1nbjp9exmq3", "cmcocefn00001760iu7evhss8": "sgbovc4kot33xoiqqamco728", "cmcoczuf80000m62povh3qgag": "l0fvdleokgq4psp4qw0b3w2o", "cmcprj22u0001qghfhsjwkvoe": "cna5dvwyaa8324tt4xwyjzf1", "cmcsoanhk0000ka6c8do81y5m": "r1filhkwvjxrdflei8ilb4go", "cmcsur5230000ww77n9zp4hss": "sa91io2m151zj76bgz0r146s", "cmcwwu9v30000yknqrmo4dhra": "t4a9wq6nbuyqcowg4ey222m6", "cmcx5u6ua00008okfrhpes7o3": "j8v7gfaygfxpcqwet88gcvyk", "cmcx7ytyl0000q4uxxytyc0w4": "bh56dta18t9ci3r5255dol3w", "cmcyeiijc0000137zm3lavf12": "yzurjocml347i9r42kb76uc9", "cmcyu6p8f0000uhzjiky1d13w": "gjrxf8ptru5v7wkon1o8y1xu", "cmcyu7vxu00019v2si9kqinvv": "n3fzcqpl6ml6vmbc7fwet5rl", "cmcztzki70000xwgvd3yodef4": "xoqstitg3aey1vtene0p7l3e", "cmczvp1ao0002p4vn5bupbkma": "wecmhk4h3lev2xcji5jg522j", "cmczvwrjp0000dwru993lndii": "d0s1w90fd5y99e8xiao1zgz4", "cmd08u4570000pe94rta19ced": "oxy9llmmyq1ndptha26nyc1m", "cmd0a73zs000010xhhxhdpf8r": "x6fc8xwnyxjapyzwloc22szd"}}}