# CRM Browser Testing Checklist

## 🎯 Testing URL: http://localhost:3002

## 📋 ADMIN Role Testing Checklist (+998901234567 / admin123)

### ✅ Login & Authentication
- [ ] Login page loads correctly
- [ ] ADMIN credentials work
- [ ] Dashboard redirects after login
- [ ] Session persists on page refresh
- [ ] Logout works properly

### ✅ Dashboard Testing
- [ ] Dashboard loads without errors
- [ ] Admin-specific widgets display
- [ ] Branch switcher appears (ADMIN only)
- [ ] Analytics cards show data
- [ ] No console errors in browser dev tools

### ✅ Students Module (/dashboard/students)
- [ ] Students list loads
- [ ] Student data displays correctly (name, phone, email, level, branch)
- [ ] "Add Student" button works
- [ ] Student creation form opens
- [ ] Student creation form submits successfully
- [ ] New student appears in list
- [ ] Student edit functionality works
- [ ] Student deletion works
- [ ] Search/filter functionality works
- [ ] Pagination works (if applicable)

### ✅ Users Module (/dashboard/users) - ADMIN ONLY
- [ ] Users list loads
- [ ] User data displays correctly
- [ ] "Add User" button works
- [ ] User creation form works
- [ ] User editing works
- [ ] User deletion works
- [ ] Role assignment works

### ✅ Payments Module (/dashboard/payments) - ADMIN & CASHIER
- [ ] Payments list loads
- [ ] Payment data displays correctly
- [ ] Student names appear in payments (not student.user.name)
- [ ] "Add Payment" button works
- [ ] Payment creation form works
- [ ] Payment editing works
- [ ] Payment deletion works

### ✅ Groups Module (/dashboard/groups)
- [ ] Groups list loads
- [ ] Group data displays correctly
- [ ] Teacher assignments show correctly
- [ ] Student enrollment counts show
- [ ] "Add Group" button works
- [ ] Group creation form works
- [ ] Group editing works
- [ ] Student enrollment/unenrollment works

### ✅ Teachers Module (/dashboard/teachers)
- [ ] Teachers list loads
- [ ] Teacher data displays correctly
- [ ] "Add Teacher" button works
- [ ] Teacher creation form works
- [ ] Teacher editing works
- [ ] Teacher deletion works
- [ ] KPI data displays

### ✅ Leads Module (/dashboard/leads)
- [ ] Leads list loads
- [ ] Lead data displays correctly
- [ ] "Add Lead" button works
- [ ] Lead creation form works
- [ ] Lead editing works
- [ ] Lead to student conversion works

### ✅ Cabinets Module (/dashboard/cabinets)
- [ ] Cabinets list loads
- [ ] Cabinet data displays correctly
- [ ] "Add Cabinet" button works
- [ ] Cabinet creation form works
- [ ] Cabinet editing works
- [ ] Cabinet deletion works

### ✅ Analytics Module (/dashboard/analytics) - ADMIN ONLY
- [ ] Analytics page loads
- [ ] Financial charts display
- [ ] Revenue data shows
- [ ] Student analytics show
- [ ] No access for non-admin roles

### ✅ Communication Module (/dashboard/communication)
- [ ] Communication page loads
- [ ] Message templates list loads
- [ ] Template creation works
- [ ] Template editing works
- [ ] Template deletion works

### ✅ Activity Logs (/dashboard/admin/activity-logs) - ADMIN ONLY
- [ ] Activity logs page loads
- [ ] Log entries display
- [ ] Filtering works
- [ ] No access for non-admin roles

### ✅ KPIs (/dashboard/admin/kpis) - ADMIN ONLY
- [ ] KPIs page loads
- [ ] KPI data displays
- [ ] Charts render correctly
- [ ] No access for non-admin roles

### ✅ Settings (/dashboard/settings)
- [ ] Settings page loads
- [ ] Settings can be viewed
- [ ] Settings can be updated
- [ ] Changes save correctly

### ✅ Branch Management (/dashboard/branch-test) - ADMIN ONLY
- [ ] Branch management page loads
- [ ] Branch switcher works
- [ ] Branch data filters correctly
- [ ] No access for non-admin roles

## 📋 MANAGER Role Testing Checklist (+998901234568 / manager123)

### ✅ Access Control Testing
- [ ] Can access: Dashboard, Students, Leads, Teachers, Groups, Cabinets, Communication, Settings
- [ ] Cannot access: Users, Analytics, Activity Logs, KPIs, Branch Management
- [ ] Proper error/redirect for restricted pages

### ✅ Functionality Testing
- [ ] All accessible pages load correctly
- [ ] CRUD operations work on accessible modules
- [ ] No admin-specific features visible
- [ ] Role-appropriate dashboard displays

## 📋 RECEPTION Role Testing Checklist (+998901234569 / reception123)

### ✅ Access Control Testing
- [ ] Can access: Dashboard, Students, Leads, Groups, Communication
- [ ] Cannot access: Teachers, Cabinets, Payments, Analytics, Users, etc.
- [ ] Proper error/redirect for restricted pages

### ✅ Functionality Testing
- [ ] All accessible pages load correctly
- [ ] Student management works
- [ ] Lead management works
- [ ] Group viewing works
- [ ] Communication tools work

## 📋 CASHIER Role Testing Checklist (+998901234570 / cashier123)

### ✅ Access Control Testing
- [ ] Can access: Dashboard, Students, Payments
- [ ] Cannot access: All other modules
- [ ] Proper error/redirect for restricted pages

### ✅ Functionality Testing
- [ ] Dashboard loads with cashier-specific data
- [ ] Students list accessible (for payment purposes)
- [ ] Payments module fully functional
- [ ] Payment creation/editing works

## 🔗 Cross-Module Relationship Testing

### ✅ Student-Payment Relationships
- [ ] Create student, then create payment for that student
- [ ] Verify payment shows correct student name
- [ ] Verify student profile shows payment history
- [ ] Edit student name, verify payment updates

### ✅ Student-Group Enrollments
- [ ] Enroll student in group
- [ ] Verify enrollment appears in student profile
- [ ] Verify student count updates in group
- [ ] Unenroll student, verify updates

### ✅ Group-Teacher Assignments
- [ ] Assign teacher to group
- [ ] Verify teacher appears in group details
- [ ] Verify group appears in teacher profile
- [ ] Change teacher assignment, verify updates

### ✅ Course-Group Associations
- [ ] Create course, then create group for that course
- [ ] Verify group shows correct course information
- [ ] Verify course shows associated groups

## 🚨 Error Scenarios to Test

### ✅ Network/API Errors
- [ ] Test with slow network (throttle in dev tools)
- [ ] Test form submission with invalid data
- [ ] Test deletion of items with dependencies
- [ ] Test concurrent edits (multiple browser tabs)

### ✅ Data Validation
- [ ] Submit forms with missing required fields
- [ ] Submit forms with invalid data formats
- [ ] Test duplicate phone numbers/emails
- [ ] Test invalid date ranges

### ✅ Edge Cases
- [ ] Test with empty database tables
- [ ] Test with large amounts of data
- [ ] Test browser back/forward navigation
- [ ] Test page refresh during operations

## 📊 Performance Testing
- [ ] Page load times under 3 seconds
- [ ] Form submissions under 2 seconds
- [ ] Search results under 1 second
- [ ] No memory leaks (check dev tools)
- [ ] No console errors or warnings

## 🎯 Testing Results Summary
**Total Test Items**: ~150 test cases
**Completed**: 0
**Passed**: 0
**Failed**: 0
**Critical Issues**: 0
**Major Issues**: 0
**Minor Issues**: 0
