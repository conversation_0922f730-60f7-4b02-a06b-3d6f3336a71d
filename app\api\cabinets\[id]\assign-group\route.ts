import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const assignGroupSchema = z.object({
  groupId: z.string().min(1, 'Group ID is required'),
})

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: cabinetId } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to assign groups to cabinets
    if (!session.user.role || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = assignGroupSchema.parse(body)

    // Check if cabinet exists
    const cabinet = await prisma.cabinet.findUnique({
      where: { id: cabinetId },
      include: {
        groups: true,
      },
    })

    if (!cabinet) {
      return NextResponse.json({ error: 'Cabinet not found' }, { status: 404 })
    }

    // Check if group exists and is not already assigned to a cabinet
    const group = await prisma.group.findUnique({
      where: { id: validatedData.groupId },
    })

    if (!group) {
      return NextResponse.json({ error: 'Group not found' }, { status: 404 })
    }

    if (group.cabinetId) {
      return NextResponse.json(
        { error: 'Group is already assigned to a cabinet' },
        { status: 400 }
      )
    }

    // Check if cabinet has capacity for another group
    if (cabinet.groups.length >= cabinet.capacity) {
      return NextResponse.json(
        { error: 'Cabinet has reached its maximum capacity' },
        { status: 400 }
      )
    }

    // Assign group to cabinet
    const updatedGroup = await prisma.group.update({
      where: { id: validatedData.groupId },
      data: { cabinetId },
      include: {
        course: {
          select: {
            name: true,
            level: true,
          },
        },
        teacher: {
          include: {
            user: {
              select: {
                name: true,
              },
            },
          },
        },
        cabinet: {
          select: {
            name: true,
            number: true,
          },
        },
      },
    })

    // Log activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: (session.user.role as Role) || Role.ADMIN,
      action: 'UPDATE',
      resource: 'GROUP',
      resourceId: updatedGroup.id,
      details: `Assigned group ${updatedGroup.name} to cabinet ${cabinet.name} (${cabinet.number})`,
    })

    return NextResponse.json({
      message: 'Group assigned to cabinet successfully',
      group: updatedGroup,
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error assigning group to cabinet:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Remove group from cabinet
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: cabinetId } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission
    if (!session.user.role || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const groupId = searchParams.get('groupId')

    if (!groupId) {
      return NextResponse.json({ error: 'Group ID is required' }, { status: 400 })
    }

    // Check if group exists and is assigned to this cabinet
    const group = await prisma.group.findUnique({
      where: { id: groupId },
      include: {
        cabinet: true,
      },
    })

    if (!group) {
      return NextResponse.json({ error: 'Group not found' }, { status: 404 })
    }

    if (group.cabinetId !== cabinetId) {
      return NextResponse.json(
        { error: 'Group is not assigned to this cabinet' },
        { status: 400 }
      )
    }

    // Remove group from cabinet
    const updatedGroup = await prisma.group.update({
      where: { id: groupId },
      data: { cabinetId: null },
      include: {
        course: {
          select: {
            name: true,
            level: true,
          },
        },
        teacher: {
          include: {
            user: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    })

    // Log activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: (session.user.role as Role) || Role.ADMIN,
      action: 'UPDATE',
      resource: 'GROUP',
      resourceId: updatedGroup.id,
      details: `Removed group ${updatedGroup.name} from cabinet ${group.cabinet?.name} (${group.cabinet?.number})`,
    })

    return NextResponse.json({
      message: 'Group removed from cabinet successfully',
      group: updatedGroup,
    })
  } catch (error) {
    console.error('Error removing group from cabinet:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
