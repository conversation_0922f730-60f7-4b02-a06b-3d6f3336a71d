"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useSession } from "next-auth/react"
import { Menu, X, Home, Users, GraduationCap, CreditCard, BarChart3, Settings, Building } from "lucide-react"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, SheetTrigger } from "@/components/sheet"
import { Badge } from "@/components/badge"

interface NavigationItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  roles: string[]
  badge?: string
}

const navigationItems: NavigationItem[] = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: Home,
    roles: ["ADMIN", "MANAGER", "RECEPTION", "CASHIER"],
  },
  {
    name: "Students",
    href: "/dashboard/students",
    icon: GraduationCap,
    roles: ["ADMIN", "MANAGE<PERSON>", "<PERSON>ECEP<PERSON><PERSON>", "CASHIE<PERSON>"],
  },
  {
    name: "Groups",
    href: "/dashboard/groups",
    icon: Users,
    roles: ["ADMIN", "MANAGER", "RECEP<PERSON>ON", "CASHIER"],
  },
  {
    name: "Payments",
    href: "/dashboard/payments",
    icon: CreditCard,
    roles: ["ADMIN", "MANAGER", "RECEPTION", "CASHIER"],
  },
  {
    name: "Users",
    href: "/dashboard/users",
    icon: Users,
    roles: ["ADMIN", "MANAGER"],
  },
  {
    name: "Reports",
    href: "/dashboard/reports",
    icon: BarChart3,
    roles: ["ADMIN", "MANAGER"],
  },
  {
    name: "Branches",
    href: "/dashboard/branches",
    icon: Building,
    roles: ["ADMIN"],
  },
  {
    name: "Settings",
    href: "/dashboard/settings",
    icon: Settings,
    roles: ["ADMIN", "MANAGER"],
  },
]

interface MobileNavProps {
  className?: string
}

export function MobileNav({ className }: MobileNavProps) {
  const [open, setOpen] = React.useState(false)
  const pathname = usePathname()
  const { data: session } = useSession()

  const userRole = session?.user?.role

  const filteredItems = navigationItems.filter((item) =>
    userRole ? item.roles.includes(userRole) : false
  )

  return (
    <div className={cn("md:hidden", className)}>
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="h-9 w-9"
          >
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle navigation menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-[300px] p-0">
          <div className="flex h-full flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b">
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                  <span className="text-primary-foreground font-bold text-sm">IC</span>
                </div>
                <span className="font-semibold">Innovative Centre</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setOpen(false)}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Navigation */}
            <nav className="flex-1 overflow-y-auto p-4">
              <div className="space-y-2">
                {filteredItems.map((item) => {
                  const isActive = pathname === item.href || pathname.startsWith(item.href + "/")
                  
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      onClick={() => setOpen(false)}
                      className={cn(
                        "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200",
                        "hover:bg-accent hover:text-accent-foreground",
                        isActive
                          ? "bg-primary text-primary-foreground shadow-sm"
                          : "text-muted-foreground"
                      )}
                    >
                      <item.icon className="h-4 w-4" />
                      <span className="flex-1">{item.name}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="ml-auto">
                          {item.badge}
                        </Badge>
                      )}
                    </Link>
                  )
                })}
              </div>
            </nav>

            {/* User info */}
            {session?.user && (
              <div className="border-t p-4">
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                    <span className="text-xs font-medium">
                      {session.user.name?.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {session.user.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {session.user.role}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </SheetContent>
      </Sheet>
    </div>
  )
}

// Bottom navigation for mobile (alternative approach)
export function MobileBottomNav() {
  const pathname = usePathname()
  const { data: session } = useSession()

  const userRole = session?.user?.role

  // Core navigation items for bottom nav
  const bottomNavItems = [
    {
      name: "Home",
      href: "/dashboard",
      icon: Home,
      roles: ["ADMIN", "MANAGER", "RECEPTION", "CASHIER"],
    },
    {
      name: "Students",
      href: "/dashboard/students",
      icon: GraduationCap,
      roles: ["ADMIN", "MANAGER", "RECEPTION", "CASHIER"],
    },
    {
      name: "Groups",
      href: "/dashboard/groups",
      icon: Users,
      roles: ["ADMIN", "MANAGER", "RECEPTION", "CASHIER"],
    },
    {
      name: "Payments",
      href: "/dashboard/payments",
      icon: CreditCard,
      roles: ["ADMIN", "MANAGER", "RECEPTION", "CASHIER"],
    },
    {
      name: "More",
      href: "/dashboard/settings",
      icon: Settings,
      roles: ["ADMIN", "MANAGER", "RECEPTION", "CASHIER"],
    },
  ]

  const filteredItems = bottomNavItems.filter((item) =>
    userRole ? item.roles.includes(userRole) : false
  ).slice(0, 5) // Limit to 5 items for bottom nav

  return (
    <div className="md:hidden fixed bottom-0 left-0 right-0 z-50 bg-background border-t">
      <nav className="flex items-center justify-around px-2 py-2">
        {filteredItems.map((item) => {
          const isActive = pathname === item.href || pathname.startsWith(item.href + "/")
          
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex flex-col items-center gap-1 px-3 py-2 rounded-lg transition-all duration-200",
                "hover:bg-accent hover:text-accent-foreground",
                isActive
                  ? "text-primary bg-primary/10"
                  : "text-muted-foreground"
              )}
            >
              <item.icon className="h-5 w-5" />
              <span className="text-xs font-medium">{item.name}</span>
            </Link>
          )
        })}
      </nav>
    </div>
  )
}

// Touch-friendly button component for mobile
interface TouchButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
  variant?: "default" | "outline" | "ghost"
  size?: "sm" | "md" | "lg"
}

export function TouchButton({ 
  children, 
  className, 
  variant = "default", 
  size = "md",
  ...props 
}: TouchButtonProps) {
  const sizeClasses = {
    sm: "h-10 px-4 text-sm",
    md: "h-12 px-6 text-base",
    lg: "h-14 px-8 text-lg",
  }

  const variantClasses = {
    default: "bg-primary text-primary-foreground hover:bg-primary/90",
    outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
    ghost: "hover:bg-accent hover:text-accent-foreground",
  }

  return (
    <button
      className={cn(
        "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50",
        "touch-manipulation", // Improves touch responsiveness
        sizeClasses[size],
        variantClasses[variant],
        className
      )}
      {...props}
    >
      {children}
    </button>
  )
}
