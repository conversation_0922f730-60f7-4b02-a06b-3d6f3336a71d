const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createAllRoleCredentials() {
  console.log('🔐 Creating test credentials for all roles...')

  try {
    // Define all role credentials
    const roleCredentials = [
      {
        role: 'ADMIN',
        phone: '+998906006299',
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'Parviz0106$'
      },
      {
        role: 'MANAGER',
        phone: '+998901111111',
        name: 'Manager User',
        email: '<EMAIL>',
        password: 'manager123'
      },
      {
        role: 'TEACHER',
        phone: '+998902222222',
        name: 'Teacher User',
        email: '<EMAIL>',
        password: 'teacher123'
      },
      {
        role: 'RECEPTION',
        phone: '+998903333333',
        name: 'Reception User',
        email: '<EMAIL>',
        password: 'reception123'
      },
      {
        role: 'CASHIER',
        phone: '+998904444444',
        name: 'Cashier User',
        email: '<EMAIL>',
        password: 'cashier123'
      },
      {
        role: 'ACADEMIC_MANAGER',
        phone: '+998905555555',
        name: 'Academic Manager User',
        email: '<EMAIL>',
        password: 'academic123'
      },
      {
        role: 'STUDENT',
        phone: '+998906666666',
        name: 'Student User',
        email: '<EMAIL>',
        password: 'student123'
      }
    ]

    console.log('\n📝 Creating/updating users for each role:')

    for (const credential of roleCredentials) {
      const hashedPassword = await bcrypt.hash(credential.password, 10)

      // Check if user exists by phone
      const existingUser = await prisma.user.findUnique({
        where: { phone: credential.phone }
      })

      let user
      if (existingUser) {
        // Update existing user
        user = await prisma.user.update({
          where: { phone: credential.phone },
          data: {
            name: credential.name,
            role: credential.role,
            password: hashedPassword,
            // Only update email if it's different to avoid unique constraint
            ...(existingUser.email !== credential.email && { email: credential.email })
          },
        })
      } else {
        // Create new user
        user = await prisma.user.create({
          data: {
            phone: credential.phone,
            name: credential.name,
            email: credential.email,
            role: credential.role,
            password: hashedPassword,
          },
        })
      }

      console.log(`✅ ${credential.role}: ${credential.phone} / ${credential.password}`)
    }

    // Create student profile for the test student
    const studentUser = await prisma.user.findUnique({
      where: { phone: '+998906666666' }
    })

    if (studentUser) {
      await prisma.student.upsert({
        where: { userId: studentUser.id },
        update: {},
        create: {
          userId: studentUser.id,
          level: 'B1',
          branch: 'Main Branch',
          emergencyContact: '+998907777777',
          dateOfBirth: new Date('2000-01-01'),
          status: 'ACTIVE'
        }
      })
      console.log('✅ Student profile created for test student')
    }

    // Create teacher profile for the test teacher
    const teacherUser = await prisma.user.findUnique({
      where: { phone: '+998902222222' }
    })

    if (teacherUser) {
      await prisma.teacher.upsert({
        where: { userId: teacherUser.id },
        update: {},
        create: {
          userId: teacherUser.id,
          subject: 'English',
          experience: 5,
          branch: 'Main Branch',
          tier: 'B_LEVEL',
          qualifications: 'TESOL Certified'
        }
      })
      console.log('✅ Teacher profile created for test teacher')
    }

    console.log('\n🎉 All role credentials created successfully!')
    console.log('\n📋 COMPLETE CREDENTIALS LIST:')
    console.log('================================')
    
    roleCredentials.forEach(cred => {
      console.log(`${cred.role.padEnd(16)} | ${cred.phone} | ${cred.password}`)
    })

    console.log('\n💡 Use these credentials to test different role permissions')
    console.log('💡 All passwords are simple for testing purposes')

  } catch (error) {
    console.error('❌ Error creating role credentials:', error.message)
    console.error('Full error:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
createAllRoleCredentials()
  .catch((error) => {
    console.error('Script failed:', error)
    process.exit(1)
  })
