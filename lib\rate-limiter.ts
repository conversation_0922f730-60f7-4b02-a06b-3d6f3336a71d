/**
 * Rate Limiter - Prevent API abuse and DDoS attacks
 * 
 * This module provides rate limiting functionality for API endpoints
 * using a memory-based sliding window approach.
 */

import { NextRequest, NextResponse } from 'next/server'

interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  message?: string // Custom error message
  skipSuccessfulRequests?: boolean // Don't count successful requests
  skipFailedRequests?: boolean // Don't count failed requests
}

interface RequestRecord {
  count: number
  resetTime: number
  requests: number[] // Timestamps of requests
}

// In-memory store for rate limiting (use Redis in production)
const requestStore = new Map<string, RequestRecord>()

// Default configurations for different endpoint types
export const RATE_LIMIT_CONFIGS = {
  // Authentication endpoints - stricter limits
  AUTH: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per 15 minutes
    message: 'Too many authentication attempts. Please try again later.'
  },
  
  // General API endpoints
  API: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // 100 requests per minute
    message: 'Too many requests. Please slow down.'
  },
  
  // Data modification endpoints (POST, PUT, DELETE)
  MUTATION: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30, // 30 mutations per minute
    message: 'Too many data modifications. Please slow down.'
  },
  
  // File upload endpoints
  UPLOAD: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 uploads per minute
    message: 'Too many file uploads. Please wait before uploading again.'
  },
  
  // Search endpoints
  SEARCH: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60, // 60 searches per minute
    message: 'Too many search requests. Please slow down.'
  }
} as const

/**
 * Get client identifier for rate limiting
 */
function getClientId(request: NextRequest): string {
  // Try to get IP from various headers (for proxy/CDN scenarios)
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const ip = forwarded?.split(',')[0] || realIp || 'unknown'
  
  // Include user agent for additional uniqueness
  const userAgent = request.headers.get('user-agent') || 'unknown'
  
  return `${ip}:${userAgent.slice(0, 50)}`
}

/**
 * Clean up expired entries from the store
 */
function cleanupExpiredEntries(): void {
  const now = Date.now()
  for (const [key, record] of requestStore.entries()) {
    if (record.resetTime <= now) {
      requestStore.delete(key)
    }
  }
}

/**
 * Check if request should be rate limited
 */
export function checkRateLimit(
  request: NextRequest,
  config: RateLimitConfig
): { allowed: boolean; remaining: number; resetTime: number } {
  const clientId = getClientId(request)
  const now = Date.now()
  const windowStart = now - config.windowMs
  
  // Clean up expired entries periodically
  if (Math.random() < 0.01) { // 1% chance
    cleanupExpiredEntries()
  }
  
  let record = requestStore.get(clientId)
  
  if (!record || record.resetTime <= now) {
    // Create new record or reset expired one
    record = {
      count: 0,
      resetTime: now + config.windowMs,
      requests: []
    }
  }
  
  // Remove requests outside the current window
  record.requests = record.requests.filter(timestamp => timestamp > windowStart)
  record.count = record.requests.length
  
  // Check if limit exceeded
  if (record.count >= config.maxRequests) {
    requestStore.set(clientId, record)
    return {
      allowed: false,
      remaining: 0,
      resetTime: record.resetTime
    }
  }
  
  // Add current request
  record.requests.push(now)
  record.count = record.requests.length
  requestStore.set(clientId, record)
  
  return {
    allowed: true,
    remaining: config.maxRequests - record.count,
    resetTime: record.resetTime
  }
}

/**
 * Rate limiting middleware
 */
export function rateLimit(config: RateLimitConfig) {
  return (request: NextRequest): NextResponse | null => {
    const result = checkRateLimit(request, config)
    
    if (!result.allowed) {
      return NextResponse.json(
        { 
          error: config.message || 'Rate limit exceeded',
          retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000)
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': result.resetTime.toString(),
            'Retry-After': Math.ceil((result.resetTime - Date.now()) / 1000).toString()
          }
        }
      )
    }
    
    // Add rate limit headers to successful responses
    const response = NextResponse.next()
    response.headers.set('X-RateLimit-Limit', config.maxRequests.toString())
    response.headers.set('X-RateLimit-Remaining', result.remaining.toString())
    response.headers.set('X-RateLimit-Reset', result.resetTime.toString())
    
    return null // Continue to next middleware
  }
}

/**
 * Apply rate limiting to API routes
 */
export function withRateLimit(
  handler: (request: NextRequest, ...args: any[]) => Promise<NextResponse>,
  config: RateLimitConfig
) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    const rateLimitResult = rateLimit(config)(request)
    
    if (rateLimitResult) {
      return rateLimitResult
    }
    
    const response = await handler(request, ...args)
    
    // Add rate limit headers to the response
    const result = checkRateLimit(request, config)
    response.headers.set('X-RateLimit-Limit', config.maxRequests.toString())
    response.headers.set('X-RateLimit-Remaining', result.remaining.toString())
    response.headers.set('X-RateLimit-Reset', result.resetTime.toString())
    
    return response
  }
}

/**
 * Get rate limit status for a client
 */
export function getRateLimitStatus(
  request: NextRequest,
  config: RateLimitConfig
): { remaining: number; resetTime: number; total: number } {
  const result = checkRateLimit(request, config)
  return {
    remaining: result.remaining,
    resetTime: result.resetTime,
    total: config.maxRequests
  }
}
