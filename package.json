{"name": "inno-crm", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:seed": "tsx prisma/seed.ts", "postinstall": "prisma generate", "prepare-deployment": "node scripts/prepare-deployment.js", "migration:analyze": "node scripts/migration/analyze.js", "migration:dry-run": "node scripts/migration/migrate.js --dry-run --verbose", "migration:execute": "node scripts/migration/migrate.js --verbose", "migration:quick": "node scripts/migration/migrate.js"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@next-auth/prisma-adapter": "^1.0.7", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^5.22.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.2.7", "@remixicon/react": "^4.6.0", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query": "^5.8.4", "@tanstack/react-table": "^8.21.3", "@types/nodemailer": "^6.4.17", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^2.30.0", "isomorphic-dompurify": "^2.26.0", "lucide-react": "^0.294.0", "next": "^14.2.30", "next-auth": "^4.24.5", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "prisma": "^5.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.57.0", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.3.6", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.6"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20.8.10", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-config-next": "14.2.15", "postcss": "^8.4.31", "tsx": "^4.19.4", "tw-animate-css": "^1.3.5", "typescript": "^5.2.2"}}