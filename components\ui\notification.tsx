import { TriangleAlertIcon, XIcon, CheckCircleIcon, InfoIcon, AlertCircleIcon } from "lucide-react"
import { Button } from "@/components/button"
import { cn } from "@/lib/utils"
import { cva, type VariantProps } from "class-variance-authority"

const notificationVariants = cva(
  "bg-background z-50 max-w-[400px] rounded-md border p-4 shadow-lg relative",
  {
    variants: {
      variant: {
        default: "border-border bg-background",
        success: "border-green-200/50 bg-green-50/50 dark:border-green-800/50 dark:bg-green-950/50",
        warning: "border-amber-200/50 bg-amber-50/50 dark:border-amber-800/50 dark:bg-amber-950/50",
        error: "border-red-200/50 bg-red-50/50 dark:border-red-800/50 dark:bg-red-950/50",
        info: "border-blue-200/50 bg-blue-50/50 dark:border-blue-800/50 dark:bg-blue-950/50",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const iconVariants = cva(
  "mt-0.5 shrink-0",
  {
    variants: {
      variant: {
        default: "text-muted-foreground",
        success: "text-green-600 dark:text-green-400",
        warning: "text-amber-600 dark:text-amber-400",
        error: "text-red-600 dark:text-red-400",
        info: "text-blue-600 dark:text-blue-400",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

interface NotificationProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof notificationVariants> {
  title: string
  description: string
  action?: {
    label: string
    onClick: () => void
  }
  onClose?: () => void
  fixed?: boolean
}

function getIcon(variant: NotificationProps["variant"]) {
  switch (variant) {
    case "success":
      return CheckCircleIcon
    case "warning":
      return TriangleAlertIcon
    case "error":
      return AlertCircleIcon
    case "info":
      return InfoIcon
    default:
      return InfoIcon
  }
}

export function Notification({
  className,
  variant,
  title,
  description,
  action,
  onClose,
  fixed = false,
  ...props
}: NotificationProps) {
  const Icon = getIcon(variant)

  return (
    <div 
      className={cn(
        notificationVariants({ variant }),
        fixed && "fixed bottom-4 right-4",
        className
      )}
      {...props}
    >
      <div className="flex gap-2">
        <div className="flex grow gap-3">
          <Icon
            className={cn(iconVariants({ variant }))}
            size={16}
            aria-hidden="true"
          />
          <div className="flex grow flex-col gap-3">
            <div className="space-y-1">
              <p className="text-sm font-medium">
                {title}
              </p>
              <p className="text-muted-foreground text-sm">
                {description}
              </p>
            </div>
            {action && (
              <div>
                <Button size="sm" onClick={action.onClick}>
                  {action.label}
                </Button>
              </div>
            )}
          </div>
        </div>
        {onClose && (
          <Button
            variant="ghost"
            className="group -my-1.5 -me-2 size-8 shrink-0 p-0 hover:bg-transparent"
            aria-label="Close notification"
            onClick={onClose}
          >
            <XIcon
              size={16}
              className="opacity-60 transition-opacity group-hover:opacity-100"
              aria-hidden="true"
            />
          </Button>
        )}
      </div>
    </div>
  )
}

// Example usage components for testing
export function NotificationExamples() {
  return (
    <div className="space-y-4 p-4">
      <Notification
        variant="warning"
        title="Something requires your action!"
        description="It conveys that a specific action is needed to resolve or address a situation."
        action={{
          label: "Learn more",
          onClick: () => console.log("Learn more clicked")
        }}
        onClose={() => console.log("Notification closed")}
      />
      
      <Notification
        variant="success"
        title="Payment processed successfully"
        description="The student's payment has been recorded and their enrollment is now active."
        action={{
          label: "View details",
          onClick: () => console.log("View details clicked")
        }}
        onClose={() => console.log("Notification closed")}
      />
      
      <Notification
        variant="error"
        title="Failed to send SMS"
        description="Unable to send enrollment confirmation SMS. Please check the phone number and try again."
        action={{
          label: "Retry",
          onClick: () => console.log("Retry clicked")
        }}
        onClose={() => console.log("Notification closed")}
      />
      
      <Notification
        variant="info"
        title="New student enrolled"
        description="John Doe has been enrolled in the Advanced English course starting next Monday."
        onClose={() => console.log("Notification closed")}
      />
    </div>
  )
}
