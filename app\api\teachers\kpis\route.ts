import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only allow certain roles to view teacher KPIs
    const userRole = (session.user as any).role
    if (!['ADMIN', 'MANAGER'].includes(userRole)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const requestedBranch = searchParams.get('branch')
    const period = searchParams.get('period') || '30' // days

    // Determine branch filter
    const userBranch = (session.user as any).branch
    let branchFilter: string

    if (userRole === 'ADMIN') {
      branchFilter = requestedBranch || 'main'
    } else {
      branchFilter = userBranch || 'main'
    }

    // Calculate date range for period
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(endDate.getDate() - parseInt(period))

    // Get teachers with detailed data for KPIs
    const teachers = await prisma.teacher.findMany({
      where: {
        branch: branchFilter,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        groups: {
          include: {
            enrollments: {
              where: {
                createdAt: {
                  gte: startDate,
                  lte: endDate,
                },
              },
              include: {
                student: {
                  select: {
                    id: true,
                    name: true,
                    status: true,
                    payments: {
                      where: {
                        createdAt: {
                          gte: startDate,
                          lte: endDate,
                        },
                      },
                    },
                  },
                },
              },
            },
            currentStudents: {
              select: {
                id: true,
                status: true,
              },
            },
          },
        },
        classes: {
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate,
            },
          },
        },
      },
    })

    // Calculate aggregate KPIs
    let totalNewStudents = 0
    let totalRevenue = 0
    let totalActiveStudents = 0
    let totalClasses = 0

    teachers.forEach(teacher => {
      teacher.groups.forEach(group => {
        totalNewStudents += group.enrollments.length
        totalActiveStudents += group.currentStudents.filter(s => s.status === 'ACTIVE').length

        group.enrollments.forEach(enrollment => {
          enrollment.student.payments.forEach(payment => {
            totalRevenue += Number(payment.amount)
          })
        })
      })
      totalClasses += teacher.classes.length
    })

    const kpis = {
      period: `${period} days`,
      dateRange: {
        start: startDate.toISOString(),
        end: endDate.toISOString(),
      },
      summary: {
        totalTeachers: teachers.length,
        totalNewStudents,
        totalRevenue,
        totalActiveStudents,
        totalClasses,
        averageStudentsPerTeacher: teachers.length > 0 ? Math.round(totalActiveStudents / teachers.length) : 0,
        averageRevenuePerTeacher: teachers.length > 0 ? Math.round(totalRevenue / teachers.length) : 0,
      },
      teachers: teachers.map(teacher => ({
        id: teacher.id,
        name: teacher.user.name,
        email: teacher.user.email,
        tier: teacher.tier,
        subject: teacher.subject,
        groupsCount: teacher.groups.length,
        newStudents: teacher.groups.reduce((sum, group) => sum + group.enrollments.length, 0),
        activeStudents: teacher.groups.reduce((sum, group) =>
          sum + group.currentStudents.filter(s => s.status === 'ACTIVE').length, 0
        ),
        classesHeld: teacher.classes.length,
        revenue: teacher.groups.reduce((sum, group) =>
          sum + group.enrollments.reduce((enrollSum, enrollment) =>
            enrollSum + enrollment.student.payments.reduce((paySum, payment) =>
              paySum + Number(payment.amount), 0
            ), 0
          ), 0
        ),
      })),
    }

    return NextResponse.json(kpis)
  } catch (error) {
    // Log error only in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error fetching teacher KPIs:', error)
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
