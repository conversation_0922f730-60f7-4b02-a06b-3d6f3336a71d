import { useDashboardStore } from '@/lib/stores/dashboard-store'

/**
 * Hook to provide dashboard data refresh functionality after form submissions
 */
export function useDashboardRefresh() {
  const { refreshSpecificData, refreshData } = useDashboardStore()

  /**
   * Refresh all dashboard data
   */
  const refreshAll = async () => {
    await refreshData()
  }

  /**
   * Refresh data after student-related operations
   * Affects: enrollments, progress, stats
   */
  const refreshAfterStudentChange = async () => {
    await refreshSpecificData(['enrollments', 'progress', 'stats'])
  }

  /**
   * Refresh data after payment operations
   * Affects: revenue, stats
   */
  const refreshAfterPayment = async () => {
    await refreshSpecificData(['revenue', 'stats'])
  }



  /**
   * Refresh data after enrollment operations
   * Affects: enrollments, stats
   */
  const refreshAfterEnrollment = async () => {
    await refreshSpecificData(['enrollments', 'stats'])
  }

  /**
   * Refresh data after lead conversion
   * Affects: enrollments, stats
   */
  const refreshAfterLeadConversion = async () => {
    await refreshSpecificData(['enrollments', 'stats'])
  }

  /**
   * Refresh data after teacher operations
   * Affects: stats
   */
  const refreshAfterTeacher = async () => {
    await refreshSpecificData(['stats'])
  }

  /**
   * Refresh data after course operations
   * Affects: enrollments, stats
   */
  const refreshAfterCourse = async () => {
    await refreshSpecificData(['enrollments', 'stats'])
  }

  return {
    refreshAll,
    refreshAfterStudentChange,
    refreshAfterPayment,
    refreshAfterEnrollment,
    refreshAfterLeadConversion,
    refreshAfterTeacher,
    refreshAfterCourse,
  }
}
