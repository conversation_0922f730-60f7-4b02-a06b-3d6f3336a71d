const { PrismaClient } = require('@prisma/client')

async function migrateStudentModel() {
  const prisma = new PrismaClient()

  try {
    console.log('🔄 Starting Student Model Migration...')

    // Step 1: Check current data
    const studentsCount = await prisma.student.count()
    const usersCount = await prisma.user.count()
    
    console.log(`📊 Current data:`)
    console.log(`   - Students: ${studentsCount}`)
    console.log(`   - Users: ${usersCount}`)

    if (studentsCount === 0) {
      console.log('✅ No students to migrate!')
      return
    }

    // Step 2: Get all students with their user data
    const studentsWithUsers = await prisma.student.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
          }
        }
      }
    })

    console.log(`\n🔄 Found ${studentsWithUsers.length} students to migrate`)

    // Step 3: Check if migration is needed
    const firstStudent = studentsWithUsers[0]
    if (firstStudent && firstStudent.name) {
      console.log('✅ Students already have direct name/phone/email fields!')
      console.log('   Migration may have already been completed.')
      return
    }

    // Step 4: Migrate each student
    console.log('\n🔄 Migrating student data...')
    
    for (const student of studentsWithUsers) {
      if (!student.user) {
        console.log(`⚠️  Student ${student.id} has no associated user - skipping`)
        continue
      }

      try {
        // Use raw SQL to update the student record
        await prisma.$executeRaw`
          UPDATE students 
          SET 
            name = ${student.user.name},
            phone = ${student.user.phone},
            email = ${student.user.email || null}
          WHERE id = ${student.id}
        `
        
        console.log(`✅ Migrated student: ${student.user.name} (${student.user.phone})`)
      } catch (error) {
        console.error(`❌ Failed to migrate student ${student.id}:`, error.message)
      }
    }

    // Step 5: Verify migration
    const migratedStudents = await prisma.$queryRaw`
      SELECT id, name, phone, email 
      FROM students 
      WHERE name IS NOT NULL 
      LIMIT 5
    `

    console.log('\n✅ Migration completed!')
    console.log('📊 Sample migrated students:')
    migratedStudents.forEach(student => {
      console.log(`   - ${student.name} (${student.phone})`)
    })

    console.log('\n⚠️  IMPORTANT NEXT STEPS:')
    console.log('1. Update your Prisma schema to remove User relation from Student')
    console.log('2. Run: npx prisma db push')
    console.log('3. Update your API endpoints to use direct student fields')
    console.log('4. Test thoroughly before removing userId column')

  } catch (error) {
    console.error('❌ Migration failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateStudentModel()
    .then(() => {
      console.log('🎉 Migration script completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Migration script failed:', error)
      process.exit(1)
    })
}

module.exports = { migrateStudentModel }
