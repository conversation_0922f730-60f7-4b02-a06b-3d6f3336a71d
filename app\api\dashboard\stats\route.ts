import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Role-based dashboard stats - redirect to appropriate endpoint
    const userRole = session.user.role

    switch (userRole) {
      case 'ADMIN':
        return NextResponse.redirect(new URL('/api/dashboard/admin-stats', request.url))
      case 'MANAGER':
        return NextResponse.redirect(new URL('/api/dashboard/manager-stats', request.url))
      case 'TEACHER':
        return NextResponse.redirect(new URL('/api/dashboard/teacher-stats', request.url))
      case 'RECEPTION':
        return NextResponse.redirect(new URL('/api/dashboard/reception-stats', request.url))
      case 'CASHIER':
        return NextResponse.redirect(new URL('/api/dashboard/cashier-stats', request.url))
      case 'ACADEMIC_MANAGER':
        return NextResponse.redirect(new URL('/api/dashboard/academic-manager-stats', request.url))
      case 'STUDENT':
        return NextResponse.redirect(new URL('/api/dashboard/student-stats', request.url))
      default:
        return NextResponse.json({ error: 'Unknown user role' }, { status: 403 })
    }


  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'Just now'
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
  }
}

function formatTime(dateString: string): string {
  const date = new Date(dateString)
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })
}
