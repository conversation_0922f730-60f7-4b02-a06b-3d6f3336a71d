'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/button'
import { Badge } from '@/components/badge'
import { Input } from '@/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { formatDate } from '@/lib/utils'
import { Search, Plus, Users, Calendar, Clock, User, Edit, Trash2, Loader2, BookOpen, ChevronDown, ChevronUp, UserPlus, MapPin } from 'lucide-react'
import GroupForm from '@/components/forms/group-form'
import StudentForm from '@/components/forms/student-form'
import { useBranch } from '@/contexts/branch-context'
import CoursesTab from '@/components/groups/courses-tab'
import { useToast } from '@/hooks/use-toast'

interface Group {
  id: string
  name: string
  capacity: number
  schedule: string
  room?: string
  branch: string
  startDate: string
  endDate: string
  isActive: boolean
  language?: string
  course: {
    name: string
    level: string
  }
  teacher: {
    id: string
    tier: string
    subject: string
    user: {
      name: string
    }
  }
  enrollments: {
    id: string
    status: string
    startDate: string
    endDate?: string
    student: {
      id: string
      user: {
        id: string
        name: string
        phone: string
      }
    }
  }[]
  _count: {
    enrollments: number
  }
}

interface SlotTierAnalysis {
  slotKey: string
  courseLevel: string
  days: string
  time: string
  tierUtilization: {
    tier: string
    utilizationRate: number
    groupCount: number
  }[]
  availableTiers: string[]
  totalGroups: number
}

// Helper functions for teacher tier styling
const getTeacherTierStyle = (tier: string) => {
  switch (tier) {
    case 'A_LEVEL':
      return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold'
    case 'B_LEVEL':
      return 'bg-gradient-to-r from-blue-400 to-blue-600 text-white font-medium'
    case 'C_LEVEL':
      return 'bg-gradient-to-r from-green-400 to-green-600 text-white'
    case 'NEW':
      return 'bg-gradient-to-r from-gray-400 to-gray-600 text-white'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getTeacherTierLabel = (tier: string) => {
  switch (tier) {
    case 'A_LEVEL':
      return 'A-Level'
    case 'B_LEVEL':
      return 'B-Level'
    case 'C_LEVEL':
      return 'C-Level'
    case 'NEW':
      return 'New'
    default:
      return 'Unknown'
  }
}



// Group Card Component
function GroupCard({
  group,
  onEdit,
  onDelete,
  onAddStudent,
  onToggleExpansion,
  isExpanded
}: {
  group: Group
  onEdit: (group: Group) => void
  onDelete: (groupId: string) => void
  onAddStudent: (group: Group) => void
  onToggleExpansion: (groupId: string) => void
  isExpanded: boolean
}) {
  const getLevelColor = (level: string) => {
    const colors: { [key: string]: string } = {
      'A1': 'bg-red-100 text-red-800',
      'A2': 'bg-orange-100 text-orange-800',
      'B1': 'bg-yellow-100 text-yellow-800',
      'B2': 'bg-green-100 text-green-800',
      'IELTS': 'bg-indigo-100 text-indigo-800',
      'SAT': 'bg-cyan-100 text-cyan-800',
      'MATH': 'bg-emerald-100 text-emerald-800',
      'KIDS': 'bg-pink-100 text-pink-800',
    }
    return colors[level] || 'bg-gray-100 text-gray-800'
  }

  const getTimeFromSchedule = (schedule: string) => {
    try {
      const parsed = JSON.parse(schedule)
      if (Array.isArray(parsed) && parsed.length > 0) {
        const firstSlot = parsed[0]
        return `${firstSlot.startTime} - ${firstSlot.endTime}`
      }
    } catch (error) {
      // Fallback to regex for old format
      const timeMatch = schedule.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/)
      return timeMatch ? `${timeMatch[1]}:${timeMatch[2]} - ${timeMatch[3]}:${timeMatch[4]}` : ''
    }
    return ''
  }

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
  }

  const getLanguageColor = (language: string) => {
    const colors: { [key: string]: string } = {
      'UZBEK': 'bg-blue-100 text-blue-800',
      'RUSSIAN': 'bg-purple-100 text-purple-800',
      'MIXED': 'bg-orange-100 text-orange-800',
    }
    return colors[language] || 'bg-gray-100 text-gray-800'
  }

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Header with Level and Student Count */}
          <div className="flex justify-between items-start">
            <div className="flex items-center space-x-3">
              <Badge className={getLevelColor(group.course.level)} variant="secondary">
                {group.course.level.replace('_', ' ')}
              </Badge>
              <Badge className="bg-blue-100 text-blue-800" variant="secondary">
                {group._count.enrollments}/{group.capacity} students
              </Badge>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(group)}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(group.id)}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Group Name */}
          <div>
            <h4 className="text-lg font-semibold text-gray-900">{group.name}</h4>
            <p className="text-sm text-gray-600">{group.course.name}</p>
          </div>

          {/* Teacher */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-gray-400" />
              <span className="text-sm font-medium">Teacher: {group.teacher.user.name}</span>
            </div>
            <span className={`text-xs px-2 py-1 rounded-full ${getTeacherTierStyle(group.teacher.tier || 'NEW')}`}>
              {getTeacherTierLabel(group.teacher.tier || 'NEW')}
            </span>
          </div>

          {/* Time */}
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-gray-400" />
            <span className="text-sm">{getTimeFromSchedule(group.schedule)}</span>
          </div>

          {/* Start Date */}
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <span className="text-sm">Start date: {formatDate(new Date(group.startDate))}</span>
          </div>

          {/* Status Badges */}
          <div className="flex flex-wrap gap-2">
            <Badge className={getStatusColor(group.isActive)} variant="secondary">
              {group.isActive ? 'Started' : 'Not Started'}
            </Badge>
            {group.language && (
              <Badge className={getLanguageColor(group.language)} variant="secondary">
                {group.language}
              </Badge>
            )}
          </div>

          {/* Students Section */}
          <div className="space-y-3 pt-4 border-t">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-gray-400" />
                <span className="text-sm font-medium">
                  Students ({group._count.enrollments}/{group.capacity})
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onAddStudent(group)}
                  disabled={group._count.enrollments >= group.capacity}
                  className="text-green-600 hover:text-green-700"
                >
                  <UserPlus className="h-4 w-4 mr-1" />
                  Add Student
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onToggleExpansion(group.id)}
                >
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            {/* Collapsible Student List */}
            {isExpanded && (
              <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                {group.enrollments.length > 0 ? (
                  group.enrollments
                    .filter(enrollment => enrollment.status === 'ACTIVE')
                    .map((enrollment) => (
                      <div key={enrollment.id} className="flex justify-between items-center text-sm">
                        <div>
                          <span className="font-medium">{enrollment.student.name}</span>
                          <span className="text-gray-500 ml-2">{enrollment.student.phone}</span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {enrollment.status}
                        </Badge>
                      </div>
                    ))
                ) : (
                  <p className="text-sm text-gray-500 text-center py-2">No students enrolled</p>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default function GroupsPage() {
  const { currentBranch } = useBranch()
  const { toast } = useToast()
  const [groups, setGroups] = useState<Group[]>([])
  const [slotTierAnalysis, setSlotTierAnalysis] = useState<SlotTierAnalysis[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [levelFilter, setLevelFilter] = useState('')
  const [languageFilter, setLanguageFilter] = useState('')
  const [scheduleFilter, setScheduleFilter] = useState('')
  const [teacherTierFilter, setTeacherTierFilter] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingGroup, setEditingGroup] = useState<Group | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set())
  const [isAddStudentDialogOpen, setIsAddStudentDialogOpen] = useState(false)
  const [selectedGroupForStudent, setSelectedGroupForStudent] = useState<Group | null>(null)
  const [availableStudents, setAvailableStudents] = useState<any[]>([])
  const [filteredStudents, setFilteredStudents] = useState<any[]>([])
  const [studentSearchTerm, setStudentSearchTerm] = useState('')
  const [isLoadingStudents, setIsLoadingStudents] = useState(false)
  const [isCreateStudentDialogOpen, setIsCreateStudentDialogOpen] = useState(false)

  useEffect(() => {
    if (currentBranch?.id) {
      fetchGroups()
    }
  }, [currentBranch?.id])

  const fetchGroups = async () => {
    if (!currentBranch?.id) return

    try {
      setLoading(true)
      const response = await fetch(`/api/groups?branch=${currentBranch.id}&limit=1000`)
      const data = await response.json()
      setGroups(data.groups || [])
      setSlotTierAnalysis(data.slotTierAnalysis || [])
      setError(null)
    } catch (error) {
      console.error('Error fetching groups:', error)
      setError('Failed to fetch groups')
    } finally {
      setLoading(false)
    }
  }

  // Handle group creation
  const handleCreateGroup = async (data: any) => {
    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch('/api/groups', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create group')
      }

      setIsCreateDialogOpen(false)
      fetchGroups() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle group update
  const handleUpdateGroup = async (data: any) => {
    if (!editingGroup) return

    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch(`/api/groups/${editingGroup.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update group')
      }

      setIsEditDialogOpen(false)
      setEditingGroup(null)
      fetchGroups() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle group deletion
  const handleDeleteGroup = async (groupId: string) => {
    if (!confirm('Are you sure you want to delete this group? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/groups/${groupId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete group')
      }

      fetchGroups() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    }
  }

  // Student management functions
  const toggleGroupExpansion = (groupId: string) => {
    const newExpanded = new Set(expandedGroups)
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId)
    } else {
      newExpanded.add(groupId)
    }
    setExpandedGroups(newExpanded)
  }

  const fetchAvailableStudents = async (groupId: string) => {
    try {
      setIsLoadingStudents(true)
      const response = await fetch(`/api/students?available=true&branch=${currentBranch?.id}`)
      const data = await response.json()
      setAvailableStudents(data.students || [])
      setFilteredStudents(data.students || [])
      setStudentSearchTerm('')
    } catch (error) {
      console.error('Error fetching students:', error)
      toast({
        title: "Error",
        description: "Failed to fetch available students",
        variant: "destructive"
      })
    } finally {
      setIsLoadingStudents(false)
    }
  }

  // Helper function to parse schedule and extract time and days (same logic as backend)
  const parseSchedule = (schedule: string) => {
    try {
      // First try to parse as JSON
      const parsed = JSON.parse(schedule)
      if (Array.isArray(parsed) && parsed.length > 0) {
        const firstSlot = parsed[0]
        const time = `${firstSlot.startTime}-${firstSlot.endTime}`

        // Extract days from the schedule array
        const dayNames = parsed.map(slot => slot.day.toLowerCase())
        const days = dayNames.includes('monday') && dayNames.includes('wednesday') && dayNames.includes('friday') ? 'MWF' :
                    dayNames.includes('tuesday') && dayNames.includes('thursday') && dayNames.includes('saturday') ? 'TTS' : 'Other'

        return { time, days }
      }
    } catch (error) {
      // Fallback to old string parsing method
      const scheduleStr = typeof schedule === 'string' ? schedule : JSON.stringify(schedule)

      // Extract time (e.g., "9:00-11:00" or "14:00-16:00")
      const timeMatch = scheduleStr.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/)
      const time = timeMatch ? `${timeMatch[1]}:${timeMatch[2]}-${timeMatch[3]}:${timeMatch[4]}` : 'Unknown'

      // Extract days (M/W/F or T/T/S)
      const lowerSchedule = scheduleStr.toLowerCase()
      const days = lowerSchedule.includes('monday') &&
                  lowerSchedule.includes('wednesday') &&
                  lowerSchedule.includes('friday') ? 'MWF' :
                  lowerSchedule.includes('tuesday') &&
                  lowerSchedule.includes('thursday') &&
                  lowerSchedule.includes('saturday') ? 'TTS' : 'Other'

      return { time, days }
    }

    return { time: 'Unknown', days: 'Unknown' }
  }

  const checkTeacherTierAvailability = async (group: Group): Promise<boolean> => {
    try {
      const response = await fetch(`/api/groups?branch=${currentBranch?.id}&limit=1000`)
      const data = await response.json()

      if (data.slotTierAnalysis) {
        // Parse the group's schedule to get the exact slot key
        const { time, days } = parseSchedule(group.schedule)
        const slotKey = `${group.course.level}-${days}-${time}`

        // Find the exact slot that matches this group's course level, days, and time
        const groupSlot = data.slotTierAnalysis.find((slot: any) => {
          return slot.slotKey === slotKey
        })

        if (groupSlot) {
          return groupSlot.availableTiers.includes(group.teacher.tier)
        }
      }

      return true
    } catch (error) {
      console.error('Error checking tier availability:', error)
      return true
    }
  }

  const handleAddStudent = async (group: Group) => {
    const teacherTier = group.teacher.tier || 'NEW'

    if (teacherTier !== 'A_LEVEL') {
      const isAvailable = await checkTeacherTierAvailability(group)
      if (!isAvailable) {
        toast({
          title: "Teacher Tier Restriction",
          description: `${getTeacherTierLabel(teacherTier)} teachers are not available for new students yet. Higher tier teachers must reach 80% capacity first.`,
          variant: "destructive"
        })
        return
      }
    }

    setSelectedGroupForStudent(group)
    await fetchAvailableStudents(group.id)
    setIsAddStudentDialogOpen(true)
  }

  // Filter students based on search term
  const handleStudentSearch = (searchTerm: string) => {
    setStudentSearchTerm(searchTerm)
    if (!searchTerm.trim()) {
      setFilteredStudents(availableStudents)
    } else {
      const filtered = availableStudents.filter(student =>
        student.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.user.phone.includes(searchTerm) ||
        student.level.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredStudents(filtered)
    }
  }

  const handleEnrollStudent = async (studentId: string) => {
    if (!selectedGroupForStudent) return

    try {
      setIsSubmitting(true)
      const response = await fetch('/api/enrollments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          studentId,
          groupId: selectedGroupForStudent.id,
          startDate: new Date().toISOString(),
          status: 'ACTIVE',
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to enroll student')
      }

      await fetchGroups()
      setIsAddStudentDialogOpen(false)
      setSelectedGroupForStudent(null)
      toast({
        title: "Success",
        description: "Student enrolled successfully",
        variant: "success"
      })
    } catch (error) {
      console.error('Error enrolling student:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to enroll student',
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle creating a new student and enrolling them
  const handleCreateAndEnrollStudent = async (data: any) => {
    if (!selectedGroupForStudent || !currentBranch) return

    try {
      setIsSubmitting(true)

      // First create the user
      const userResponse = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: data.name,
          phone: data.phone,
          email: data.email || undefined,
          role: 'STUDENT',
          password: 'defaultPassword123' // Default password for new students
        })
      })

      if (!userResponse.ok) {
        const errorData = await userResponse.json()
        throw new Error(errorData.error || 'Failed to create user')
      }

      const userData = await userResponse.json()

      // Then create the student profile
      const studentResponse = await fetch('/api/students', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: userData.id,
          level: data.level,
          branch: currentBranch.id,
          emergencyContact: data.emergencyContact,
          dateOfBirth: data.dateOfBirth,
          address: data.address
        })
      })

      if (!studentResponse.ok) {
        const errorData = await studentResponse.json()
        throw new Error(errorData.error || 'Failed to create student')
      }

      const studentData = await studentResponse.json()

      // Finally enroll the student in the group
      const enrollmentResponse = await fetch('/api/enrollments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          studentId: studentData.id,
          groupId: selectedGroupForStudent.id,
          startDate: new Date().toISOString(),
          status: 'ACTIVE',
        }),
      })

      if (!enrollmentResponse.ok) {
        const error = await enrollmentResponse.json()
        throw new Error(error.error || 'Failed to enroll student')
      }

      await fetchGroups()
      setIsCreateStudentDialogOpen(false)
      setIsAddStudentDialogOpen(false)
      setSelectedGroupForStudent(null)
      toast({
        title: "Success",
        description: "Student created and enrolled successfully",
        variant: "success"
      })
    } catch (error) {
      console.error('Error creating and enrolling student:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to create and enroll student',
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Filter groups based on all criteria
  const filteredGroups = groups.filter(group => {
    const matchesSearch = group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      group.course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      group.teacher.user.name.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesLevel = !levelFilter || levelFilter === 'all' || group.course.level === levelFilter
    const matchesLanguage = !languageFilter || languageFilter === 'all' || group.language === languageFilter
    const matchesTeacherTier = !teacherTierFilter || teacherTierFilter === 'all' || group.teacher.tier === teacherTierFilter

    // Schedule filter based on time period
    let matchesSchedule = true
    if (scheduleFilter) {
      const timePeriod = getTimePeriod(group.schedule).toLowerCase()
      matchesSchedule = timePeriod === scheduleFilter
    }

    return matchesSearch && matchesLevel && matchesLanguage && matchesSchedule && matchesTeacherTier
  }).sort((a, b) => {
    // Sort by teacher tier priority (A_LEVEL first, then B_LEVEL, C_LEVEL, NEW)
    const tierPriority: Record<string, number> = { 'A_LEVEL': 1, 'B_LEVEL': 2, 'C_LEVEL': 3, 'NEW': 4 }
    const aTier = tierPriority[a.teacher.tier || 'NEW'] || 4
    const bTier = tierPriority[b.teacher.tier || 'NEW'] || 4
    return aTier - bTier
  })

  // Separate groups by schedule type
  const mwfGroups = filteredGroups.filter(group => {
    try {
      const parsed = JSON.parse(group.schedule)
      if (Array.isArray(parsed)) {
        const dayNames = parsed.map(slot => slot.day.toLowerCase())
        return dayNames.includes('monday') && dayNames.includes('wednesday') && dayNames.includes('friday')
      }
    } catch (error) {
      // Fallback to string search
      return group.schedule.toLowerCase().includes('monday') &&
             group.schedule.toLowerCase().includes('wednesday') &&
             group.schedule.toLowerCase().includes('friday')
    }
    return false
  })

  const ttsGroups = filteredGroups.filter(group => {
    try {
      const parsed = JSON.parse(group.schedule)
      if (Array.isArray(parsed)) {
        const dayNames = parsed.map(slot => slot.day.toLowerCase())
        return dayNames.includes('tuesday') && dayNames.includes('thursday') && dayNames.includes('saturday')
      }
    } catch (error) {
      // Fallback to string search
      return group.schedule.toLowerCase().includes('tuesday') &&
             group.schedule.toLowerCase().includes('thursday') &&
             group.schedule.toLowerCase().includes('saturday')
    }
    return false
  })

  // Helper function to determine schedule type
  function getScheduleType(schedule: string): string {
    try {
      const parsed = JSON.parse(schedule)
      if (Array.isArray(parsed)) {
        const dayNames = parsed.map(slot => slot.day.toLowerCase())
        if (dayNames.includes('monday') && dayNames.includes('wednesday') && dayNames.includes('friday')) {
          return 'mwf'
        }
        if (dayNames.includes('tuesday') && dayNames.includes('thursday') && dayNames.includes('saturday')) {
          return 'tts'
        }
        return 'other'
      }
    } catch (error) {
      // Fallback to string search
      const lowerSchedule = schedule.toLowerCase()
      if (lowerSchedule.includes('monday') && lowerSchedule.includes('wednesday') && lowerSchedule.includes('friday')) {
        return 'mwf'
      }
      if (lowerSchedule.includes('tuesday') && lowerSchedule.includes('thursday') && lowerSchedule.includes('saturday')) {
        return 'tts'
      }
    }
    return 'other'
  }

  // Helper function to get time period from schedule
  function getTimePeriod(schedule: string): string {
    try {
      const parsed = JSON.parse(schedule)
      if (Array.isArray(parsed) && parsed.length > 0) {
        const firstSlot = parsed[0]
        const startHour = parseInt(firstSlot.startTime.split(':')[0])
        if (startHour < 12) return 'Morning'
        if (startHour < 17) return 'Afternoon'
        return 'Evening'
      }
    } catch (error) {
      // Fallback to regex
      const timeMatch = schedule.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/)
      if (timeMatch) {
        const startHour = parseInt(timeMatch[1])
        if (startHour < 12) return 'Morning'
        if (startHour < 17) return 'Afternoon'
        return 'Evening'
      }
    }
    return ''
  }



  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading groups...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Groups</h1>
          <p className="text-gray-600">Manage class groups and schedules</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-blue-600 hover:bg-blue-700">
              Add New Group
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Group</DialogTitle>
              <DialogDescription>
                Set up a new class group with course, teacher, and schedule details.
              </DialogDescription>
            </DialogHeader>
            <GroupForm
              onSubmit={handleCreateGroup}
              onCancel={() => setIsCreateDialogOpen(false)}
              isEditing={false}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="groups" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="groups" className="flex items-center">
            <Users className="h-4 w-4 mr-2" />
            Groups
          </TabsTrigger>
          <TabsTrigger value="courses" className="flex items-center">
            <BookOpen className="h-4 w-4 mr-2" />
            Courses
          </TabsTrigger>
        </TabsList>

        <TabsContent value="groups" className="space-y-6">
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Select Level</label>
              <Select value={levelFilter} onValueChange={setLevelFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="A1">A1</SelectItem>
                  <SelectItem value="A2">A2</SelectItem>
                  <SelectItem value="B1">B1</SelectItem>
                  <SelectItem value="B2">B2</SelectItem>
                  <SelectItem value="IELTS">IELTS</SelectItem>
                  <SelectItem value="SAT">SAT</SelectItem>
                  <SelectItem value="MATH">MATH</SelectItem>
                  <SelectItem value="KIDS">KIDS</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Language</label>
              <Select value={languageFilter} onValueChange={setLanguageFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Languages" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Languages</SelectItem>
                  <SelectItem value="UZBEK">Uzbek</SelectItem>
                  <SelectItem value="RUSSIAN">Russian</SelectItem>
                  <SelectItem value="MIXED">Mixed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Teacher Tier</label>
              <Select value={teacherTierFilter} onValueChange={setTeacherTierFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Tiers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tiers</SelectItem>
                  <SelectItem value="A_LEVEL">
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${getTeacherTierStyle('A_LEVEL')}`}>
                        A-Level
                      </span>
                      <span>A-Level Teachers</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="B_LEVEL">
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${getTeacherTierStyle('B_LEVEL')}`}>
                        B-Level
                      </span>
                      <span>B-Level Teachers</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="C_LEVEL">
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${getTeacherTierStyle('C_LEVEL')}`}>
                        C-Level
                      </span>
                      <span>C-Level Teachers</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="NEW">
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${getTeacherTierStyle('NEW')}`}>
                        New
                      </span>
                      <span>New Teachers</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="relative">
              <Search className="absolute left-3 top-8 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <label className="text-sm font-medium text-gray-700">Search</label>
              <Input
                placeholder="Search by teacher or group name"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Schedule Filter Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={scheduleFilter === '' ? 'default' : 'outline'}
              onClick={() => setScheduleFilter('')}
              className="text-sm"
            >
              Available Groups
            </Button>
            <Button
              variant={scheduleFilter === 'morning' ? 'default' : 'outline'}
              onClick={() => setScheduleFilter('morning')}
              className="text-sm"
            >
              Morning Groups
            </Button>
            <Button
              variant={scheduleFilter === 'afternoon' ? 'default' : 'outline'}
              onClick={() => setScheduleFilter('afternoon')}
              className="text-sm"
            >
              Afternoon Groups
            </Button>
            <Button
              variant={scheduleFilter === 'evening' ? 'default' : 'outline'}
              onClick={() => setScheduleFilter('evening')}
              className="text-sm"
            >
              Evening Groups
            </Button>
          </div>

          {/* Two-Column Layout for Groups */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Monday/Wednesday/Friday Groups */}
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-blue-600">Monday/Wednesday/Friday</h3>
              <div className="space-y-4">
                {mwfGroups.map((group) => (
                  <GroupCard
                    key={group.id}
                    group={group}
                    onEdit={(group) => {
                      setEditingGroup(group)
                      setIsEditDialogOpen(true)
                    }}
                    onDelete={handleDeleteGroup}
                    onAddStudent={handleAddStudent}
                    onToggleExpansion={toggleGroupExpansion}
                    isExpanded={expandedGroups.has(group.id)}
                  />
                ))}
                {mwfGroups.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No M/W/F groups found
                  </div>
                )}
              </div>
            </div>

            {/* Tuesday/Thursday/Saturday Groups */}
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-blue-600">Tuesday/Thursday/Saturday</h3>
              <div className="space-y-4">
                {ttsGroups.map((group) => (
                  <GroupCard
                    key={group.id}
                    group={group}
                    onEdit={(group) => {
                      setEditingGroup(group)
                      setIsEditDialogOpen(true)
                    }}
                    onDelete={handleDeleteGroup}
                    onAddStudent={handleAddStudent}
                    onToggleExpansion={toggleGroupExpansion}
                    isExpanded={expandedGroups.has(group.id)}
                  />
                ))}
                {ttsGroups.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No T/T/S groups found
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Groups</p>
                <p className="text-2xl font-bold text-gray-900">{groups.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Groups</p>
                <p className="text-2xl font-bold text-gray-900">
                  {groups.filter(g => g.isActive).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">
                  {groups.reduce((sum, group) => sum + group._count.enrollments, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg. Group Size</p>
                <p className="text-2xl font-bold text-gray-900">
                  {groups.length > 0 
                    ? Math.round(groups.reduce((sum, group) => sum + group._count.enrollments, 0) / groups.length)
                    : 0
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
          </div>
        </TabsContent>

        <TabsContent value="courses" className="space-y-6">
          <CoursesTab />
        </TabsContent>
      </Tabs>

      {/* Edit Group Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Group</DialogTitle>
            <DialogDescription>
              Update group information, schedule, and settings.
            </DialogDescription>
          </DialogHeader>
          {editingGroup && (
            <GroupForm
              initialData={{
                name: editingGroup.name,
                courseId: '', // You'll need to get this from the group data
                teacherId: '', // You'll need to get this from the group data
                capacity: editingGroup.capacity,
                schedule: editingGroup.schedule,
                room: editingGroup.room || '',
                branch: editingGroup.branch,
                startDate: new Date(editingGroup.startDate).toISOString().split('T')[0],
                endDate: new Date(editingGroup.endDate).toISOString().split('T')[0],
                isActive: editingGroup.isActive
              }}
              onSubmit={handleUpdateGroup}
              onCancel={() => {
                setIsEditDialogOpen(false)
                setEditingGroup(null)
              }}
              isEditing={true}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Add Student Dialog */}
      <Dialog open={isAddStudentDialogOpen} onOpenChange={setIsAddStudentDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Add Student to Group</DialogTitle>
            <DialogDescription>
              {selectedGroupForStudent && (
                <>Select a student to enroll in <strong>{selectedGroupForStudent.name}</strong></>
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {/* Search and Create Student Section */}
            <div className="space-y-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search students by name, phone, or level..."
                  value={studentSearchTerm}
                  onChange={(e) => handleStudentSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button
                variant="outline"
                onClick={() => setIsCreateStudentDialogOpen(true)}
                className="w-full text-blue-600 border-blue-200 hover:bg-blue-50"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create New Student
              </Button>
            </div>

            {/* Student List */}
            {isLoadingStudents ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading students...</span>
              </div>
            ) : filteredStudents.length > 0 ? (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {filteredStudents.map((student) => (
                  <div
                    key={student.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleEnrollStudent(student.id)}
                  >
                    <div>
                      <p className="font-medium">{student.name}</p>
                      <p className="text-sm text-gray-500">{student.phone}</p>
                      <p className="text-xs text-gray-400">Level: {student.level}</p>
                    </div>
                    <Button size="sm" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        'Enroll'
                      )}
                    </Button>
                  </div>
                ))}
              </div>
            ) : availableStudents.length > 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No students found matching your search</p>
                <p className="text-sm">Try a different search term or create a new student</p>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No available students found</p>
                <p className="text-sm">All students may already be enrolled in groups</p>
              </div>
            )}
            <div className="flex justify-end space-x-2 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => {
                  setIsAddStudentDialogOpen(false)
                  setSelectedGroupForStudent(null)
                  setStudentSearchTerm('')
                  setFilteredStudents([])
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Create Student Dialog */}
      <Dialog open={isCreateStudentDialogOpen} onOpenChange={setIsCreateStudentDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New Student</DialogTitle>
            <DialogDescription>
              Create a new student and automatically enroll them in {selectedGroupForStudent?.name}
            </DialogDescription>
          </DialogHeader>
          <StudentForm
            initialData={{
              branch: currentBranch?.id || ''
            }}
            onSubmit={handleCreateAndEnrollStudent}
            onCancel={() => setIsCreateStudentDialogOpen(false)}
            isEditing={false}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
