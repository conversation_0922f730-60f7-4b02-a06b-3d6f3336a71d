import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// Types for dashboard data
export interface RevenueData {
  month: string
  revenue: number
  payments: number
  target: number
}

export interface PaymentMethodData {
  method: string
  amount: number
  percentage: number
}



export interface EnrollmentData {
  month: string
  newEnrollments: number
  totalEnrollments: number
  dropouts: number
  completions: number
  netGrowth: number
}

export interface CourseEnrollmentData {
  course: string
  level: string
  enrollments: number
  percentage: number
  groups: number
}

export interface ProgressData {
  month: string
  levelUps: number
  completions: number
  averageScore: number
  totalAssessments: number
}

export interface LevelDistributionData {
  level: string
  count: number
  percentage: number
}

export interface DashboardStats {
  totalStudents: number
  totalTeachers: number
  totalRevenue: number
  activeEnrollments: number
  attendanceRate: number
  completionRate: number
}

// Dashboard store interface
interface DashboardStore {
  // Loading states
  loading: {
    revenue: boolean
    enrollments: boolean
    progress: boolean
    stats: boolean
  }

  // Error states
  errors: {
    revenue: string | null
    enrollments: string | null
    progress: string | null
    stats: string | null
  }

  // Data
  revenue: {
    monthlyRevenue: RevenueData[]
    paymentMethods: PaymentMethodData[]
    summary: any
  }

  enrollments: {
    monthlyEnrollments: EnrollmentData[]
    courseEnrollments: CourseEnrollmentData[]
    summary: any
  }
  
  progress: {
    monthlyProgress: ProgressData[]
    levelDistribution: LevelDistributionData[]
    summary: any
  }

  stats: DashboardStats

  // Time range for all analytics
  timeRange: string

  // Actions
  setTimeRange: (range: string) => void
  setLoading: (type: keyof DashboardStore['loading'], loading: boolean) => void
  setError: (type: keyof DashboardStore['errors'], error: string | null) => void
  
  // Data fetching actions
  fetchRevenue: (range?: string) => Promise<void>
  fetchEnrollments: (range?: string) => Promise<void>
  fetchProgress: (range?: string) => Promise<void>
  fetchStats: () => Promise<void>
  fetchAllData: (range?: string) => Promise<void>
  
  // Data setters
  setRevenue: (data: any) => void
  setEnrollments: (data: any) => void
  setProgress: (data: any) => void
  setStats: (data: DashboardStats) => void
  
  // Utility actions
  refreshData: () => Promise<void>
  refreshSpecificData: (types: Array<keyof DashboardStore['loading']>) => Promise<void>
  clearErrors: () => void
}

// Create the dashboard store
export const useDashboardStore = create<DashboardStore>()(
  devtools(
    (set, get) => ({
      // Initial loading states
      loading: {
        revenue: false,
        enrollments: false,
        progress: false,
        stats: false,
      },

      // Initial error states
      errors: {
        revenue: null,
        enrollments: null,
        progress: null,
        stats: null,
      },

      // Initial data
      revenue: {
        monthlyRevenue: [],
        paymentMethods: [],
        summary: {},
      },

      enrollments: {
        monthlyEnrollments: [],
        courseEnrollments: [],
        summary: {},
      },
      
      progress: {
        monthlyProgress: [],
        levelDistribution: [],
        summary: {},
      },

      stats: {
        totalStudents: 0,
        totalTeachers: 0,
        totalRevenue: 0,
        activeEnrollments: 0,
        attendanceRate: 0,
        completionRate: 0,
      },

      timeRange: '12months',

      // Actions
      setTimeRange: (range: string) => {
        set({ timeRange: range })
        // Automatically refresh data when time range changes
        get().fetchAllData(range)
      },

      setLoading: (type, loading) => {
        set((state) => ({
          loading: { ...state.loading, [type]: loading }
        }))
      },

      setError: (type, error) => {
        set((state) => ({
          errors: { ...state.errors, [type]: error }
        }))
      },

      // Data fetching actions
      fetchRevenue: async (range) => {
        const { setLoading, setError } = get()
        const currentRange = range || get().timeRange
        
        setLoading('revenue', true)
        setError('revenue', null)
        
        try {
          const response = await fetch(`/api/analytics/revenue?range=${currentRange}`)
          if (response.ok) {
            const data = await response.json()
            get().setRevenue(data)
          } else {
            throw new Error(`Failed to fetch revenue data: ${response.status}`)
          }
        } catch (error) {
          console.error('Error fetching revenue data:', error)
          setError('revenue', error instanceof Error ? error.message : 'Failed to fetch revenue data')
        } finally {
          setLoading('revenue', false)
        }
      },



      fetchEnrollments: async (range) => {
        const { setLoading, setError } = get()
        const currentRange = range || get().timeRange
        
        setLoading('enrollments', true)
        setError('enrollments', null)
        
        try {
          const response = await fetch(`/api/analytics/enrollments?range=${currentRange}`)
          if (response.ok) {
            const data = await response.json()
            get().setEnrollments(data)
          } else {
            throw new Error(`Failed to fetch enrollment data: ${response.status}`)
          }
        } catch (error) {
          console.error('Error fetching enrollment data:', error)
          setError('enrollments', error instanceof Error ? error.message : 'Failed to fetch enrollment data')
        } finally {
          setLoading('enrollments', false)
        }
      },

      fetchProgress: async (range) => {
        const { setLoading, setError } = get()
        const currentRange = range || get().timeRange
        
        setLoading('progress', true)
        setError('progress', null)
        
        try {
          const response = await fetch(`/api/analytics/progress?range=${currentRange}`)
          if (response.ok) {
            const data = await response.json()
            get().setProgress(data)
          } else {
            throw new Error(`Failed to fetch progress data: ${response.status}`)
          }
        } catch (error) {
          console.error('Error fetching progress data:', error)
          setError('progress', error instanceof Error ? error.message : 'Failed to fetch progress data')
        } finally {
          setLoading('progress', false)
        }
      },

      fetchStats: async () => {
        const { setLoading, setError } = get()
        
        setLoading('stats', true)
        setError('stats', null)
        
        try {
          const response = await fetch('/api/dashboard/stats')
          if (response.ok) {
            const data = await response.json()
            get().setStats(data)
          } else {
            throw new Error(`Failed to fetch dashboard stats: ${response.status}`)
          }
        } catch (error) {
          console.error('Error fetching dashboard stats:', error)
          setError('stats', error instanceof Error ? error.message : 'Failed to fetch dashboard stats')
        } finally {
          setLoading('stats', false)
        }
      },

      fetchAllData: async (range) => {
        const { fetchRevenue, fetchEnrollments, fetchProgress, fetchStats } = get()

        // Fetch all data in parallel
        await Promise.all([
          fetchRevenue(range),
          fetchEnrollments(range),
          fetchProgress(range),
          fetchStats(),
        ])
      },

      // Data setters
      setRevenue: (data) => {
        set({ revenue: data })
      },

      setEnrollments: (data) => {
        set({ enrollments: data })
      },

      setProgress: (data) => {
        set({ progress: data })
      },

      setStats: (data) => {
        set({ stats: data })
      },

      // Utility actions
      refreshData: async () => {
        const { timeRange } = get()
        await get().fetchAllData(timeRange)
      },

      refreshSpecificData: async (types) => {
        const { timeRange, fetchRevenue, fetchEnrollments, fetchProgress, fetchStats } = get()

        const refreshActions = {
          revenue: () => fetchRevenue(timeRange),
          enrollments: () => fetchEnrollments(timeRange),
          progress: () => fetchProgress(timeRange),
          stats: () => fetchStats(),
        }

        // Refresh only the specified data types
        const promises = types.map(type => refreshActions[type]?.())
        await Promise.all(promises.filter(Boolean))
      },

      clearErrors: () => {
        set({
          errors: {
            revenue: null,
            enrollments: null,
            progress: null,
            stats: null,
          }
        })
      },
    }),
    {
      name: 'dashboard-store',
    }
  )
)
