/**
 * Security Middleware - Comprehensive security layer for API endpoints
 * 
 * This module provides authentication, authorization, rate limiting,
 * and input validation middleware for API routes.
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { rateLimit, RATE_LIMIT_CONFIGS } from '@/lib/rate-limiter'
import { hasAccessToBranch } from '@/lib/branch-utils'
import { errorLogger } from '@/lib/error-logging'
import { Role } from '@prisma/client'

export interface SecurityConfig {
  // Authentication requirements
  requireAuth?: boolean
  allowedRoles?: Role[]
  
  // Rate limiting
  rateLimit?: keyof typeof RATE_LIMIT_CONFIGS | {
    windowMs: number
    maxRequests: number
    message?: string
  }
  
  // Branch access control
  requireBranchAccess?: boolean
  
  // Additional security checks
  requireHttps?: boolean
  allowedOrigins?: string[]
  
  // Logging
  logAccess?: boolean
  logErrors?: boolean
}

export interface SecurityContext {
  user: {
    id: string
    role: Role
    branch: string
    name: string
    email?: string
    phone: string
  }
  clientIp: string
  userAgent: string
  requestId: string
}

/**
 * Generate unique request ID for tracking
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Get client IP address
 */
function getClientIp(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  return forwarded?.split(',')[0] || realIp || 'unknown'
}

/**
 * Authentication middleware
 */
async function authenticateRequest(
  request: NextRequest,
  config: SecurityConfig
): Promise<{ success: true; context: SecurityContext } | { success: false; response: NextResponse }> {
  if (!config.requireAuth) {
    // Create minimal context for non-authenticated requests
    return {
      success: true,
      context: {
        user: null as any,
        clientIp: getClientIp(request),
        userAgent: request.headers.get('user-agent') || 'unknown',
        requestId: generateRequestId()
      }
    }
  }

  const session = await getServerSession(authOptions)
  
  if (!session?.user) {
    return {
      success: false,
      response: NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
  }

  const user = session.user as any
  
  // Validate user has required role
  if (config.allowedRoles && !config.allowedRoles.includes(user.role)) {
    await errorLogger.logSecurityEvent({
      type: 'UNAUTHORIZED_ACCESS_ATTEMPT',
      userId: user.id,
      userRole: user.role,
      requiredRoles: config.allowedRoles,
      ip: getClientIp(request),
      userAgent: request.headers.get('user-agent') || 'unknown',
      path: request.nextUrl.pathname
    })
    
    return {
      success: false,
      response: NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }
  }

  return {
    success: true,
    context: {
      user: {
        id: user.id,
        role: user.role,
        branch: user.branch,
        name: user.name,
        email: user.email,
        phone: user.phone
      },
      clientIp: getClientIp(request),
      userAgent: request.headers.get('user-agent') || 'unknown',
      requestId: generateRequestId()
    }
  }
}

/**
 * Branch access control middleware
 */
function validateBranchAccess(
  request: NextRequest,
  context: SecurityContext,
  config: SecurityConfig
): NextResponse | null {
  if (!config.requireBranchAccess || !context.user) {
    return null
  }

  const requestedBranch = request.nextUrl.searchParams.get('branch')
  
  if (requestedBranch && !hasAccessToBranch({ user: context.user } as any, requestedBranch)) {
    errorLogger.logSecurityEvent({
      type: 'BRANCH_ACCESS_VIOLATION',
      userId: context.user.id,
      userRole: context.user.role,
      userBranch: context.user.branch,
      requestedBranch,
      ip: context.clientIp,
      userAgent: context.userAgent,
      path: request.nextUrl.pathname
    })
    
    return NextResponse.json(
      { error: 'Access denied to requested branch' },
      { status: 403 }
    )
  }

  return null
}

/**
 * HTTPS enforcement middleware
 */
function enforceHttps(request: NextRequest, config: SecurityConfig): NextResponse | null {
  if (!config.requireHttps || process.env.NODE_ENV !== 'production') {
    return null
  }

  const protocol = request.headers.get('x-forwarded-proto') || 'http'
  
  if (protocol !== 'https') {
    const httpsUrl = new URL(request.url)
    httpsUrl.protocol = 'https:'
    
    return NextResponse.redirect(httpsUrl, 301)
  }

  return null
}

/**
 * CORS validation middleware
 */
function validateCors(request: NextRequest, config: SecurityConfig): NextResponse | null {
  if (!config.allowedOrigins || config.allowedOrigins.length === 0) {
    return null
  }

  const origin = request.headers.get('origin')
  
  if (origin && !config.allowedOrigins.includes(origin)) {
    return NextResponse.json(
      { error: 'Origin not allowed' },
      { status: 403 }
    )
  }

  return null
}

/**
 * Main security middleware function
 */
export function withSecurity(
  handler: (request: NextRequest, context: SecurityContext, ...args: any[]) => Promise<NextResponse>,
  config: SecurityConfig = {}
) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    const startTime = Date.now()
    
    try {
      // 1. HTTPS enforcement
      const httpsCheck = enforceHttps(request, config)
      if (httpsCheck) return httpsCheck

      // 2. CORS validation
      const corsCheck = validateCors(request, config)
      if (corsCheck) return corsCheck

      // 3. Rate limiting
      if (config.rateLimit) {
        const rateLimitConfig = typeof config.rateLimit === 'string' 
          ? RATE_LIMIT_CONFIGS[config.rateLimit]
          : config.rateLimit
        
        const rateLimitCheck = rateLimit(rateLimitConfig)(request)
        if (rateLimitCheck) return rateLimitCheck
      }

      // 4. Authentication
      const authResult = await authenticateRequest(request, config)
      if (!authResult.success) return authResult.response

      const context = authResult.context

      // 5. Branch access control
      const branchCheck = validateBranchAccess(request, context, config)
      if (branchCheck) return branchCheck

      // 6. Log access if enabled
      if (config.logAccess && context.user) {
        errorLogger.logApiAccess({
          userId: context.user.id,
          userRole: context.user.role,
          method: request.method,
          path: request.nextUrl.pathname,
          ip: context.clientIp,
          userAgent: context.userAgent,
          requestId: context.requestId
        })
      }

      // 7. Execute handler
      const response = await handler(request, context, ...args)
      
      // 8. Add security headers
      response.headers.set('X-Content-Type-Options', 'nosniff')
      response.headers.set('X-Frame-Options', 'DENY')
      response.headers.set('X-XSS-Protection', '1; mode=block')
      response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
      response.headers.set('X-Request-ID', context.requestId)
      
      // Add timing header for monitoring
      const duration = Date.now() - startTime
      response.headers.set('X-Response-Time', `${duration}ms`)

      return response

    } catch (error) {
      // Log security-related errors
      if (config.logErrors) {
        await errorLogger.logError({
          error: error as Error,
          context: {
            path: request.nextUrl.pathname,
            method: request.method,
            ip: getClientIp(request),
            userAgent: request.headers.get('user-agent') || 'unknown'
          }
        })
      }

      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

/**
 * Predefined security configurations for common use cases
 */
export const SECURITY_CONFIGS = {
  // Public endpoints (no auth required)
  PUBLIC: {
    requireAuth: false,
    rateLimit: 'API',
    requireHttps: true,
    logAccess: false
  },

  // Authentication endpoints
  AUTH: {
    requireAuth: false,
    rateLimit: 'AUTH',
    requireHttps: true,
    logAccess: true,
    logErrors: true
  },

  // Admin-only endpoints
  ADMIN_ONLY: {
    requireAuth: true,
    allowedRoles: ['ADMIN'] as Role[],
    rateLimit: 'MUTATION',
    requireBranchAccess: false,
    requireHttps: true,
    logAccess: true,
    logErrors: true
  },

  // General API endpoints
  AUTHENTICATED: {
    requireAuth: true,
    allowedRoles: ['ADMIN', 'MANAGER', 'RECEPTION', 'CASHIER'] as Role[],
    rateLimit: 'API',
    requireBranchAccess: true,
    requireHttps: true,
    logAccess: true,
    logErrors: true
  },

  // Data modification endpoints
  MUTATION: {
    requireAuth: true,
    allowedRoles: ['ADMIN', 'MANAGER', 'RECEPTION'] as Role[],
    rateLimit: 'MUTATION',
    requireBranchAccess: true,
    requireHttps: true,
    logAccess: true,
    logErrors: true
  }
} as const
