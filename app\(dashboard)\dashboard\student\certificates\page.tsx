'use client'

import { useState, useEffect } from 'react'

interface Certificate {
  id: string
  name: string
  course: string
  completionDate?: string
  expectedCompletion?: string
  expectedStart?: string
  score?: number
  grade?: string
  certificateNumber?: string
  progress?: number
  status: string
}
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Award, 
  Download, 
  Calendar,
  CheckCircle,
  Clock,
  Star,
  FileText,
  Eye
} from 'lucide-react'

export default function StudentCertificatesPage() {
  const [certificateData, setCertificateData] = useState<{
    completed: Certificate[]
    inProgress: Certificate[]
    upcoming: Certificate[]
  }>({
    completed: [],
    inProgress: [],
    upcoming: []
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchCertificates()
  }, [])

  const fetchCertificates = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/students/certificates')
      if (response.ok) {
        const data = await response.json()
        setCertificateData(data)
      } else {
        throw new Error('Failed to fetch certificates')
      }
    } catch (error) {
      console.error('Error fetching certificates:', error)
      setError('Failed to load certificates')
    } finally {
      setLoading(false)
    }
  }

  const getGradeColor = (grade: string) => {
    switch (grade.toLowerCase()) {
      case 'excellent':
        return 'bg-green-100 text-green-800'
      case 'good':
        return 'bg-blue-100 text-blue-800'
      case 'satisfactory':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'issued':
        return 'bg-green-100 text-green-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'upcoming':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'issued':
        return <CheckCircle className="h-4 w-4" />
      case 'in_progress':
        return <Clock className="h-4 w-4" />
      case 'upcoming':
        return <Calendar className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">My Certificates</h1>
        <p className="text-gray-600">View and download your academic certificates</p>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed Certificates</CardTitle>
            <Award className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{certificateData.completed.length}</div>
            <p className="text-xs text-muted-foreground">Certificates earned</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{certificateData.inProgress.length}</div>
            <p className="text-xs text-muted-foreground">Currently studying</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
            <Calendar className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{certificateData.upcoming.length}</div>
            <p className="text-xs text-muted-foreground">Future courses</p>
          </CardContent>
        </Card>
      </div>

      {/* Completed Certificates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Award className="h-5 w-5 mr-2 text-green-600" />
            Completed Certificates
          </CardTitle>
          <CardDescription>Your earned certificates available for download</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {certificateData.completed.map((cert) => (
              <div key={cert.id} className="flex items-center justify-between p-4 border rounded-lg bg-green-50">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-full">
                    <Award className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-medium">{cert.name}</h4>
                    <p className="text-sm text-gray-600">{cert.course}</p>
                    <div className="flex items-center space-x-4 mt-1">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">Completed: {cert.completionDate}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">Score: {cert.score}%</span>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Certificate #: {cert.certificateNumber}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge className={getGradeColor(cert.grade || 'N/A')}>
                    {cert.grade || 'N/A'}
                  </Badge>
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  <Button size="sm">
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* In Progress Certificates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2 text-blue-600" />
            Certificates in Progress
          </CardTitle>
          <CardDescription>Courses you&apos;re currently taking</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {certificateData.inProgress.map((cert) => (
              <div key={cert.id} className="flex items-center justify-between p-4 border rounded-lg bg-blue-50">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full">
                    <Clock className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">{cert.name}</h4>
                    <p className="text-sm text-gray-600">{cert.course}</p>
                    <div className="flex items-center space-x-1 mt-1">
                      <Calendar className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-500">Expected completion: {cert.expectedCompletion}</span>
                    </div>
                    <div className="mt-2">
                      <div className="flex items-center justify-between text-xs mb-1">
                        <span className="text-gray-600">Progress</span>
                        <span className="font-medium">{cert.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full" 
                          style={{ width: `${cert.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
                <Badge className={getStatusColor(cert.status)}>
                  In Progress
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Upcoming Certificates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-gray-600" />
            Upcoming Certificates
          </CardTitle>
          <CardDescription>Future courses in your learning path</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {certificateData.upcoming.map((cert) => (
              <div key={cert.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full">
                    <Calendar className="h-6 w-6 text-gray-600" />
                  </div>
                  <div>
                    <h4 className="font-medium">{cert.name}</h4>
                    <p className="text-sm text-gray-600">{cert.course}</p>
                    <div className="flex items-center space-x-1 mt-1">
                      <Calendar className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-500">Expected start: {cert.expectedStart}</span>
                    </div>
                  </div>
                </div>
                <Badge className={getStatusColor(cert.status)}>
                  Upcoming
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Certificate Information */}
      <Card>
        <CardHeader>
          <CardTitle>Certificate Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 border-l-4 border-blue-400 rounded">
              <h4 className="font-medium text-blue-900">Digital Certificates</h4>
              <p className="text-sm text-blue-800 mt-1">
                All certificates are digitally signed and can be verified online using the certificate number.
              </p>
            </div>
            <div className="p-4 bg-green-50 border-l-4 border-green-400 rounded">
              <h4 className="font-medium text-green-900">International Recognition</h4>
              <p className="text-sm text-green-800 mt-1">
                Our certificates are recognized internationally and follow CEFR standards.
              </p>
            </div>
            <div className="p-4 bg-yellow-50 border-l-4 border-yellow-400 rounded">
              <h4 className="font-medium text-yellow-900">Certificate Verification</h4>
              <p className="text-sm text-yellow-800 mt-1">
                Employers can verify your certificates online at verify.innovativecentre.uz
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
