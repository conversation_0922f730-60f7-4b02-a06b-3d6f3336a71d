'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON>hart, Pie, Cell } from 'recharts'
import { formatDate } from '@/lib/utils'
import { Users, Phone, UserPlus, TrendingUp, Calendar, Download } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface ReceptionKPI {
  userId: string
  userName: string
  studentsAdded: number
}

interface CallCentreKPI {
  userId: string
  userName: string
  leadsContacted: number
}

interface KPIResponse {
  receptionKPIs: ReceptionKPI[]
  callCentreKPIs: CallCentreKPI[]
  dateRange: {
    startDate: string
    endDate: string
  }
}

export default function KPIDashboardPage() {
  const [kpiData, setKpiData] = useState<KPIResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [startDate, setStartDate] = useState(() => {
    const date = new Date()
    date.setDate(1) // First day of current month
    return date.toISOString().split('T')[0]
  })
  const [endDate, setEndDate] = useState(() => {
    return new Date().toISOString().split('T')[0]
  })
  const [dateRange, setDateRange] = useState('thisMonth')
  const { toast } = useToast()

  const fetchKPIs = useCallback(async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        startDate,
        endDate,
      })

      const response = await fetch(`/api/kpis?${params}`)
      if (!response.ok) throw new Error('Failed to fetch KPIs')

      const data: KPIResponse = await response.json()
      setKpiData(data)
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch KPI data",
      })
    } finally {
      setLoading(false)
    }
  }, [startDate, endDate, toast])

  useEffect(() => {
    fetchKPIs()
  }, [fetchKPIs])

  const handleDateRangeChange = (range: string) => {
    setDateRange(range)
    const today = new Date()
    
    switch (range) {
      case 'today':
        setStartDate(today.toISOString().split('T')[0])
        setEndDate(today.toISOString().split('T')[0])
        break
      case 'thisWeek':
        const weekStart = new Date(today)
        weekStart.setDate(today.getDate() - today.getDay())
        setStartDate(weekStart.toISOString().split('T')[0])
        setEndDate(today.toISOString().split('T')[0])
        break
      case 'thisMonth':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)
        setStartDate(monthStart.toISOString().split('T')[0])
        setEndDate(today.toISOString().split('T')[0])
        break
      case 'lastMonth':
        const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1)
        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0)
        setStartDate(lastMonthStart.toISOString().split('T')[0])
        setEndDate(lastMonthEnd.toISOString().split('T')[0])
        break
      case 'thisYear':
        const yearStart = new Date(today.getFullYear(), 0, 1)
        setStartDate(yearStart.toISOString().split('T')[0])
        setEndDate(today.toISOString().split('T')[0])
        break
    }
  }

  const totalStudentsAdded = kpiData?.receptionKPIs.reduce((sum, kpi) => sum + kpi.studentsAdded, 0) || 0
  const totalLeadsContacted = kpiData?.callCentreKPIs.reduce((sum, kpi) => sum + kpi.leadsContacted, 0) || 0

  const receptionChartData = kpiData?.receptionKPIs.map(kpi => ({
    name: kpi.userName,
    students: kpi.studentsAdded,
  })) || []

  const callCentreChartData = kpiData?.callCentreKPIs.map(kpi => ({
    name: kpi.userName,
    leads: kpi.leadsContacted,
  })) || []

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

  const exportKPIs = async () => {
    try {
      if (!kpiData) return

      const csvContent = [
        ['Staff Type', 'Staff Name', 'Metric', 'Value'].join(','),
        ...kpiData.receptionKPIs.map(kpi => [
          'Reception',
          kpi.userName,
          'Students Added',
          kpi.studentsAdded.toString()
        ].join(',')),
        ...kpiData.callCentreKPIs.map(kpi => [
          'Call Centre',
          kpi.userName,
          'Leads Contacted',
          kpi.leadsContacted.toString()
        ].join(','))
      ].join('\n')

      const blob = new Blob([csvContent], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `kpi-report-${startDate}-to-${endDate}.csv`
      a.click()
      window.URL.revokeObjectURL(url)

      toast({
        title: "Success",
        description: "KPI report exported successfully",
      })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to export KPI report",
      })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">KPI Dashboard</h1>
          <p className="text-gray-600">Track performance metrics for reception and call centre staff</p>
        </div>
        <Button onClick={exportKPIs} className="flex items-center space-x-2">
          <Download className="h-4 w-4" />
          <span>Export Report</span>
        </Button>
      </div>

      {/* Date Range Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Date Range</CardTitle>
          <CardDescription>Select the period for KPI analysis</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select value={dateRange} onValueChange={handleDateRangeChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="thisWeek">This Week</SelectItem>
                <SelectItem value="thisMonth">This Month</SelectItem>
                <SelectItem value="lastMonth">Last Month</SelectItem>
                <SelectItem value="thisYear">This Year</SelectItem>
                <SelectItem value="custom">Custom Range</SelectItem>
              </SelectContent>
            </Select>

            <Input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              placeholder="Start Date"
            />

            <Input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              placeholder="End Date"
            />

            <Button onClick={fetchKPIs} variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              Update
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* KPI Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <UserPlus className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Students Added</p>
                <p className="text-2xl font-bold text-gray-900">{totalStudentsAdded}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Phone className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Leads Contacted</p>
                <p className="text-2xl font-bold text-gray-900">{totalLeadsContacted}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Reception Staff</p>
                <p className="text-2xl font-bold text-gray-900">{kpiData?.receptionKPIs.length || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Call Centre Staff</p>
                <p className="text-2xl font-bold text-gray-900">{kpiData?.callCentreKPIs.length || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Reception Staff Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Reception Staff Performance</CardTitle>
              <CardDescription>Students added by reception staff</CardDescription>
            </CardHeader>
            <CardContent>
              {receptionChartData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={receptionChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="students" fill="#10B981" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-64 text-gray-500">
                  No data available for the selected period
                </div>
              )}
            </CardContent>
          </Card>

          {/* Call Centre Staff Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Call Centre Staff Performance</CardTitle>
              <CardDescription>Leads contacted by call centre staff</CardDescription>
            </CardHeader>
            <CardContent>
              {callCentreChartData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={callCentreChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="leads" fill="#3B82F6" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-64 text-gray-500">
                  No data available for the selected period
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Detailed Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Reception Staff Table */}
        <Card>
          <CardHeader>
            <CardTitle>Reception Staff Details</CardTitle>
            <CardDescription>Individual performance breakdown</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {kpiData?.receptionKPIs.map((kpi, index) => (
                <div key={kpi.userId} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{kpi.userName}</p>
                    <p className="text-sm text-gray-500">Reception Staff</p>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    {kpi.studentsAdded} students
                  </Badge>
                </div>
              ))}
              {(!kpiData?.receptionKPIs || kpiData.receptionKPIs.length === 0) && (
                <div className="text-center py-8 text-gray-500">
                  No reception staff activity in the selected period
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Call Centre Staff Table */}
        <Card>
          <CardHeader>
            <CardTitle>Call Centre Staff Details</CardTitle>
            <CardDescription>Individual performance breakdown</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {kpiData?.callCentreKPIs.map((kpi, index) => (
                <div key={kpi.userId} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{kpi.userName}</p>
                    <p className="text-sm text-gray-500">Call Centre Staff</p>
                  </div>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    {kpi.leadsContacted} leads
                  </Badge>
                </div>
              ))}
              {(!kpiData?.callCentreKPIs || kpiData.callCentreKPIs.length === 0) && (
                <div className="text-center py-8 text-gray-500">
                  No call centre staff activity in the selected period
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
