/**
 * Branch Utilities - Standardized branch handling across the CRM system
 * 
 * This module provides consistent branch filtering and access control
 * for all API endpoints and components.
 */

import { Session } from 'next-auth'

export interface BranchInfo {
  id: string
  name: string
  address?: string
  phone?: string
  isActive: boolean
}

// Standardized branch definitions
export const BRANCHES: BranchInfo[] = [
  {
    id: 'main',
    name: 'Main Branch',
    address: 'Gagarin 95A, Samarkand',
    phone: '+998712345678',
    isActive: true
  },
  {
    id: 'branch',
    name: 'Branch',
    address: '<PERSON><PERSON>\'bek 34, Samarkand',
    phone: '+998712345679',
    isActive: true
  }
]

export const DEFAULT_BRANCH = BRANCHES[0]

/**
 * Get branch filter for API queries based on user role and permissions
 * 
 * @param session - NextAuth session object
 * @param requestedBranch - Branch ID requested via query parameter
 * @returns Branch ID to use for database filtering
 */
export function getBranchFilter(session: Session | null, requestedBranch?: string | null): string {
  if (!session?.user) {
    return DEFAULT_BRANCH.id
  }

  const userRole = (session.user as any).role
  const userBranch = (session.user as any).branch

  if (userRole === 'ADMIN') {
    // ADMIN can view any branch or all branches
    return requestedBranch || userBranch || DEFAULT_BRANCH.id
  } else {
    // Non-admin users can only see their assigned branch
    return userBranch || DEFAULT_BRANCH.id
  }
}

/**
 * Validate if a user has access to a specific branch
 * 
 * @param session - NextAuth session object
 * @param branchId - Branch ID to check access for
 * @returns true if user has access, false otherwise
 */
export function hasAccessToBranch(session: Session | null, branchId: string): boolean {
  if (!session?.user) {
    return false
  }

  const userRole = (session.user as any).role
  const userBranch = (session.user as any).branch

  if (userRole === 'ADMIN') {
    // ADMIN has access to all branches
    return true
  } else {
    // Non-admin users can only access their assigned branch
    return userBranch === branchId
  }
}

/**
 * Get branch information by ID
 * 
 * @param branchId - Branch ID to look up
 * @returns Branch information or null if not found
 */
export function getBranchById(branchId: string): BranchInfo | null {
  return BRANCHES.find(branch => branch.id === branchId) || null
}

/**
 * Get all active branches
 * 
 * @returns Array of active branches
 */
export function getActiveBranches(): BranchInfo[] {
  return BRANCHES.filter(branch => branch.isActive)
}

/**
 * Validate branch ID
 * 
 * @param branchId - Branch ID to validate
 * @returns true if valid, false otherwise
 */
export function isValidBranchId(branchId: string): boolean {
  return BRANCHES.some(branch => branch.id === branchId)
}

/**
 * Get user's assigned branch information
 * 
 * @param session - NextAuth session object
 * @returns Branch information or default branch
 */
export function getUserBranch(session: Session | null): BranchInfo {
  if (!session?.user) {
    return DEFAULT_BRANCH
  }

  const userBranch = (session.user as any).branch
  return getBranchById(userBranch) || DEFAULT_BRANCH
}

/**
 * Create standardized where clause for branch filtering in Prisma queries
 * 
 * @param session - NextAuth session object
 * @param requestedBranch - Branch ID requested via query parameter
 * @param fieldName - Name of the branch field in the model (default: 'branch')
 * @returns Prisma where clause object
 */
export function createBranchWhereClause(
  session: Session | null, 
  requestedBranch?: string | null,
  fieldName: string = 'branch'
): Record<string, any> {
  const branchFilter = getBranchFilter(session, requestedBranch)
  return { [fieldName]: branchFilter }
}

/**
 * Create branch filter for nested relationships
 * 
 * @param session - NextAuth session object
 * @param requestedBranch - Branch ID requested via query parameter
 * @param relationPath - Path to the branch field (e.g., 'student.branch')
 * @returns Prisma where clause object for nested filtering
 */
export function createNestedBranchFilter(
  session: Session | null,
  requestedBranch?: string | null,
  relationPath: string = 'student.branch'
): Record<string, any> {
  const branchFilter = getBranchFilter(session, requestedBranch)
  const pathParts = relationPath.split('.')
  
  let result: any = branchFilter
  for (let i = pathParts.length - 1; i >= 0; i--) {
    result = { [pathParts[i]]: result }
  }
  
  return result
}
