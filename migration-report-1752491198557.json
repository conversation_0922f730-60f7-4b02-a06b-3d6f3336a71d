{"mode": "live", "startTime": "2025-07-14T11:04:38.275Z", "endTime": "2025-07-14T11:06:38.554Z", "duration": 120.268, "stats": {"cabinets": {"total": 22, "success": 22, "errors": 0}, "teachers": {"total": 34, "success": 34, "errors": 0}, "courses": {"total": 15, "success": 10, "errors": 5}, "groups": {"total": 198, "success": 169, "errors": 29}}, "errors": ["Course A2-English-387000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create(\nUnique constraint failed on the fields: (`name`)", "Course B1-English-527000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create(\nUnique constraint failed on the fields: (`name`)", "Course Math-English-492000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create(\nUnique constraint failed on the fields: (`name`)", "Course A2-English-448: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create(\nUnique constraint failed on the fields: (`name`)", "Course B2-English-487000: \nInvalid `prisma.course.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:268:29\n\n  265 }\n  266 \n  267 if (!migration.dryRun) {\n→ 268   await prisma.course.create(\nUnique constraint failed on the fields: (`name`)", "Group B2 new (02.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group B1 new (05.06): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group B2 new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group A1 new module 2: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group A1 new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group SAT English : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group A2 new (15.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A1 : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group B1 middle: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group B1 +Blue book new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B1 +Blue book new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group A2 middle: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group B2 middle: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group A1 new (nabor): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group IELTS: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group IELTS: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group B2 - pre IELTS new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group IELTS new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group A1 new (full): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group Math abiturient : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group Math 8 class: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group B2 new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group IELTS new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group IELTS: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group A1(middle): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group A1 new: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group IELTS Intensive: \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nUnique constraint failed on the fields: (`name`)", "Group A2 NEW (24.07): \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`", "Group C1 : \nInvalid `prisma.group.create()` invocation in\nC:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\scripts\\migration\\migrate.js:341:28\n\n  338 }\n  339 \n  340 if (!migration.dryRun) {\n→ 341   await prisma.group.create(\nForeign key constraint violated: `groups_courseId_fkey (index)`"], "mappings": {"cabinets": {"cm7n4pe08000110hm4tc4ocew": "hn81jhafcm24za46fcweydow", "cm7n4qdna00014r6zt9pqebgo": "uu9vsydoupcy9cwgya5cm0cu", "cm7n4rmbc00034r6zcjbiadzu": "uc6p1dml2hu7hyj520gq7lh9", "cm7n4v5uy00014x8i7p7z3318": "el76cqkwriszqsb840rined0", "cm7n4yyt500064r6zae6ui4ag": "s284byw84brm5xnsuhovyeah", "cm7n50d7b00084r6zcl1ghtnc": "zd8uy15vos6nhhcxf9549rz0", "cm7n5191000024x8i3kfl2n9y": "v64z81ljelnmgrs43be84qp1", "cm7n52jog00034x8il03s9lhe": "qy2jqjamwdf0tnm1d9fckfev", "cm7n55cgz0000123zavxf7jcu": "hged110rc7vup4h3s4m223mo", "cm7n56d330001123zryfchkqy": "cdwqc1dsx0osmf02zl6xygli", "cm7n56tee0003123z35ylpjrr": "jr5mh6de06hi3u541sagh5y4", "cm7n570m30004123zgcqo24iz": "xe91t64puy4n5qp5qjf1wcyq", "cm7n575ts0005123zmlybgbyk": "dbf94f3l8p65iw8ztpht1arw", "cm7n57b3i0006123zr531s3h9": "i7hm3b9l9gjmj1jy47p2gc07", "cm7n57rq50007123zsbl09zue": "su0etwinm7iyzn85wyd3icu4", "cm7n5977i0008123zrxl0uyaw": "qmz9nrhq4hl1ecjej9v4myph", "cm7n59ni70009123zbky7948o": "b5dknheoi94g3lhni1tg78cw", "cm7n5j1gx00011411foknb0ly": "p3wex5o4ox8gymnkfumpdhjs", "cm7n5jk7r00021411szw1mw09": "deudj54l1ovmho66iner3oi2", "cmc36mqht0008ngu3v1ct4i07": "xlrp2ixff3uev7xuo4kg61uu", "cmc3ak3kt0004a918cm045ze0": "l6hqdniwthwyyvp9v1zac9xl", "cmc97tbh100029c9i71fqjotm": "qxwhs109xr33tp951msvukzm"}, "teachers": {"cmc8mk7or0000dk5earhataio": "cnxwi0gxjbb0a9ooiki2hdua", "cmc8mrxug0000yipubq47xapy": "hxdttxgms2ybnes1wglhqnkz", "cmc8mwzuh0001yipuo462sp47": "out0nw9931qo8dc05kjb8x76", "cmc8mxm4y0002yipupix86g63": "cuu3imin6510b5xs68uv9svx", "cmc8my6n60003yipu0uf12nhg": "aevpodbah5k0eq7rzgnf550i", "cmc8mzcrs0004yipud37mpo15": "onhi0wvg3jys8os42oc1xlrw", "cmc8n0zvr0006yiputvovsmy6": "dr1cssi9qmkwsdjxmru6ssgk", "cmc8n1nv20007yipusgazu9pu": "b379zljgh25rel646jeszu3w", "cmc8n28fw0008yipuzek85hdt": "gjv9qek1wfn5dxntz9rr12q8", "cmc8n2u4i0009yipu9k5qpxhg": "vpjnhetugf07pypokqmwkoo0", "cmc8nmrkz0000hj8g13k2r49c": "wvq1nptqvbh1ghlq10sg8d3w", "cmc8no0i00001hj8gc4n7xdvp": "pqu8yvvdmee45slhvmr2lg0l", "cmc8nwq5s0000lplge4a7tt9w": "z2ginaf5ql0nkd7xraveg7pz", "cmc8nxr9o0001lplghse3n50b": "tanuq5j5n343xkm7q4zssg5u", "cmc8nynca0002lplgeeh6m1tn": "fv9eyvzrdufb3w5adgaatuz7", "cmc8o0iu40003lplgbc5s922e": "kxszg7osf4ko9opjjeel7ohj", "cmc8o1b0k0004lplg4yybh8dh": "lciedmcho5gjdl783x625hzm", "cmc8onzmr0000jp5bn8gzz7by": "e83w7n6t241vua057etweb95", "cmc8ooujp0001jp5besanhx2s": "j813gp7pjfmbabr3dw3u5qz1", "cmc8opz7z0002jp5bdxr660ui": "ojj4ijg30vjytgkwf6ur83z0", "cmc8oqwwx0003jp5b44rl2x3j": "gdm3n9wpmltdwtnuwnsfkbx9", "cmc8wzf2v0001145qgfzj21w3": "bjxbnwc4pb8npo99avfk425f", "cmc8xqrfx0007m55q6sqrb5nq": "wrtk4f1qtfrtyikxcqz4a44e", "cmc93qvxo00063tz69dkfnz1n": "b55d5gvuxwprxrvpl241uqd0", "cmca0c12g00034irplr696163": "thv05ohcz4g3vvxfdau67nyt", "cmca0hojg00054irpeqlp24xc": "dq0akia47w94nea9kdekskdk", "cmca1zhfg000011z0akg9c57c": "bdhfjyqw2x04bqqqn43j0luy", "cmca3yga00005ypbirkl6o7y8": "xyygw2gxksw2szds2bjh5aq2", "cmca4w4g80002wsb3qjkv4vfo": "tl7mqkhttnbfdk5cl82s5i90", "cmca5hduu0007wsb3li5w4jf6": "ys88ef6lhifeimjhkywohcaf", "cmcd9fc010003mva86b3lnmom": "d5uswmowtwoy6sxqk87oflnv", "cmcel210q0000jcppf0o416va": "qayr0v9yp8yybnwtcwqts0mi", "cmclio2ju0001ked72a90t0en": "bop59rllsqf2qj333kg4ot64", "cmd084odk00009pgfeb87pcm6": "eop1kyia394gdqrjcxvyz57i"}, "users": {"user_cmc8mk7or0000dk5earhataio": "gba9a605s65kbwsuf2p6971l", "cnxwi0gxjbb0a9ooiki2hdua": "gba9a605s65kbwsuf2p6971l", "user_cmc8mrxug0000yipubq47xapy": "l8uifzbfa3bepdxb47vrnq6z", "hxdttxgms2ybnes1wglhqnkz": "l8uifzbfa3bepdxb47vrnq6z", "user_cmc8mwzuh0001yipuo462sp47": "gsaxz4266mdgfzomhtxh4zwb", "out0nw9931qo8dc05kjb8x76": "gsaxz4266mdgfzomhtxh4zwb", "user_cmc8mxm4y0002yipupix86g63": "w2ijfwizuzztypfcafqond1v", "cuu3imin6510b5xs68uv9svx": "w2ijfwizuzztypfcafqond1v", "user_cmc8my6n60003yipu0uf12nhg": "vje83khhymy4xczmq48c1eue", "aevpodbah5k0eq7rzgnf550i": "vje83khhymy4xczmq48c1eue", "user_cmc8mzcrs0004yipud37mpo15": "z5fck6llp9ynw15mhitetps1", "onhi0wvg3jys8os42oc1xlrw": "z5fck6llp9ynw15mhitetps1", "user_cmc8n0zvr0006yiputvovsmy6": "vcaewkxlbjsixirwfi9iauuz", "dr1cssi9qmkwsdjxmru6ssgk": "vcaewkxlbjsixirwfi9iauuz", "user_cmc8n1nv20007yipusgazu9pu": "yxby47uw0x9rb5twfx8sla9p", "b379zljgh25rel646jeszu3w": "yxby47uw0x9rb5twfx8sla9p", "user_cmc8n28fw0008yipuzek85hdt": "lkn1xswy2d8u1gim92ni0o56", "gjv9qek1wfn5dxntz9rr12q8": "lkn1xswy2d8u1gim92ni0o56", "user_cmc8n2u4i0009yipu9k5qpxhg": "zpfgk4oo01kwx48ah04pv54c", "vpjnhetugf07pypokqmwkoo0": "zpfgk4oo01kwx48ah04pv54c", "user_cmc8nmrkz0000hj8g13k2r49c": "cub1flvkvxh8vrgpxgpzdf47", "wvq1nptqvbh1ghlq10sg8d3w": "cub1flvkvxh8vrgpxgpzdf47", "user_cmc8no0i00001hj8gc4n7xdvp": "r406zobl9v4ks3pqn4y5lwar", "pqu8yvvdmee45slhvmr2lg0l": "r406zobl9v4ks3pqn4y5lwar", "user_cmc8nwq5s0000lplge4a7tt9w": "syi68ri69z34p6qgirwfrd15", "z2ginaf5ql0nkd7xraveg7pz": "syi68ri69z34p6qgirwfrd15", "user_cmc8nxr9o0001lplghse3n50b": "ehc6r2ns9f5olbchtlq8krgv", "tanuq5j5n343xkm7q4zssg5u": "ehc6r2ns9f5olbchtlq8krgv", "user_cmc8nynca0002lplgeeh6m1tn": "fyoamieivmzpdwzlnwfqrn3v", "fv9eyvzrdufb3w5adgaatuz7": "fyoamieivmzpdwzlnwfqrn3v", "user_cmc8o0iu40003lplgbc5s922e": "q37jinqha6w18kz64i2wp6fg", "kxszg7osf4ko9opjjeel7ohj": "q37jinqha6w18kz64i2wp6fg", "user_cmc8o1b0k0004lplg4yybh8dh": "srsyhgehjxnpxzydyva2hr26", "lciedmcho5gjdl783x625hzm": "srsyhgehjxnpxzydyva2hr26", "user_cmc8onzmr0000jp5bn8gzz7by": "u58de5cqo54406546u3x1ivs", "e83w7n6t241vua057etweb95": "u58de5cqo54406546u3x1ivs", "user_cmc8ooujp0001jp5besanhx2s": "j80hkzm0lr5xtfzd215l6im8", "j813gp7pjfmbabr3dw3u5qz1": "j80hkzm0lr5xtfzd215l6im8", "user_cmc8opz7z0002jp5bdxr660ui": "peb9dpkete7eix6xswxlvkpj", "ojj4ijg30vjytgkwf6ur83z0": "peb9dpkete7eix6xswxlvkpj", "user_cmc8oqwwx0003jp5b44rl2x3j": "kf3aehfo8fi4irkffc4k7ajl", "gdm3n9wpmltdwtnuwnsfkbx9": "kf3aehfo8fi4irkffc4k7ajl", "user_cmc8wzf2v0001145qgfzj21w3": "le23g5txzjqyb92lj6az3hhv", "bjxbnwc4pb8npo99avfk425f": "le23g5txzjqyb92lj6az3hhv", "user_cmc8xqrfx0007m55q6sqrb5nq": "ubxuvhob6w6xvr1teo6gf61x", "wrtk4f1qtfrtyikxcqz4a44e": "ubxuvhob6w6xvr1teo6gf61x", "user_cmc93qvxo00063tz69dkfnz1n": "xxdfrpgsi5hrcfc85dm1i0og", "b55d5gvuxwprxrvpl241uqd0": "xxdfrpgsi5hrcfc85dm1i0og", "user_cmca0c12g00034irplr696163": "ir9sdtaziqu4jqob0t484gzr", "thv05ohcz4g3vvxfdau67nyt": "ir9sdtaziqu4jqob0t484gzr", "user_cmca0hojg00054irpeqlp24xc": "nm66tbexvvvabh2bbiylk3n4", "dq0akia47w94nea9kdekskdk": "nm66tbexvvvabh2bbiylk3n4", "user_cmca1zhfg000011z0akg9c57c": "wp3e0yk5ir9iclk5won85xqd", "bdhfjyqw2x04bqqqn43j0luy": "wp3e0yk5ir9iclk5won85xqd", "user_cmca3yga00005ypbirkl6o7y8": "kljci6ymo74shjlgyisgbnab", "xyygw2gxksw2szds2bjh5aq2": "kljci6ymo74shjlgyisgbnab", "user_cmca4w4g80002wsb3qjkv4vfo": "tv8w7qvy5lhrwka4hotllwwk", "tl7mqkhttnbfdk5cl82s5i90": "tv8w7qvy5lhrwka4hotllwwk", "user_cmca5hduu0007wsb3li5w4jf6": "w3m4ly66p4lxsx3h6jum5l20", "ys88ef6lhifeimjhkywohcaf": "w3m4ly66p4lxsx3h6jum5l20", "user_cmcd9fc010003mva86b3lnmom": "gtk334dl6jzkrz614cndxvfc", "d5uswmowtwoy6sxqk87oflnv": "gtk334dl6jzkrz614cndxvfc", "user_cmcel210q0000jcppf0o416va": "dto01c64mkflhonxnjbq6sep", "qayr0v9yp8yybnwtcwqts0mi": "dto01c64mkflhonxnjbq6sep", "user_cmclio2ju0001ked72a90t0en": "a74kq6zrk5ck3jq264ow4nxw", "bop59rllsqf2qj333kg4ot64": "a74kq6zrk5ck3jq264ow4nxw", "user_cmd084odk00009pgfeb87pcm6": "ua85ha0q7oo9jzkw5j83a2xm", "eop1kyia394gdqrjcxvyz57i": "ua85ha0q7oo9jzkw5j83a2xm"}, "courses": {"B1-English-494000": "flgrmlnjzb3jud2ikaz4x92y", "B2-English-527000": "r8msdkypuoretrfps9zbh797", "A2-English-448000": "wwpu3ulukz3lgvt9d8add4rr", "A1-English-387000": "g3ydv7r99uein5u9hmmu13ro", "Individual-English-1474000": "mjgko83h5a95b0dzldbl0v7r", "SAT-English-568000": "offw8cg7qvuzch1doxrrejb8", "Speaking-English-507000": "xio9t2gd310stwvv0z9nnbn7", "Kids-English-324000": "qalcdaawgrimkffskhkjb0ph", "IELTS-English-586000": "k10cf7uv47ktu7uojpw8zkfk", "A2-English-387000": "juzw4wqfd7wxcsqb5wpk76dq", "B1-English-527000": "dv6i3j33bfuatghsw799g7tx", "Math-English-422000": "wj7qepec8p4o4d52cptxek0l", "Math-English-492000": "fuyxmnghyiqxa295xfezu7oa", "A2-English-448": "u63we88vd503mrlf34v9j5r3", "B2-English-487000": "ene9bu2gzfqcwrj1sdqxe38a"}, "groups": {"cmc8owvo30000dnwiqsxsjoo1": "rqltyecsk2tm8yfc6zhdouvm", "cmc8oz3tx0001dnwivzgup0ty": "bepjz3nbg5wi7ohwsk24z5ri", "cmc8p3vpb0004jp5bin1nfj8l": "m7nn3s4t4ixod00xcbgc223w", "cmc8p53nw0002dnwiaqasonli": "mzkxo2trgb396bfeazxe9ipi", "cmc8p7ys60005jp5ber9vytsz": "qexyhmbd0d1q4hnihaqqscou", "cmc8p9dhp0006jp5bs65ezopv": "aqgk956hovxpa9xcxrjacwsm", "cmc8pbpl80003dnwipzso68wq": "vlu5th2n6adkz2b83p3kvp8o", "cmc8pg4rf0007jp5bwcn1pap5": "qytn2xqc7vohwjbk16es10q6", "cmc8ph5hm0004dnwitdzh2qct": "o91r8d8wl70635bo1i83qzke", "cmc8pinr40005dnwismmwxieg": "xgpftrm5u75t2pt1ztvy9amc", "cmc8pkg3i0006dnwi42o2vro9": "r5eddpbbb7hhlp063z5adno6", "cmc8pqyq10007dnwihf6qdz9m": "bsc9cotn96sfpjkt34ibrlkj", "cmc8pybf80009jp5bsd06mksa": "h18yyoh4yyuggig8w7chwbk9", "cmc8q6k1q0008dnwill3wkv1h": "w0b1jls8udep7n0g3h69lte4", "cmc8q7mb90000mx4jrqlo5p5f": "f8dr0p6077qtws597enklhz2", "cmc8q9wfo0009dnwio1nao7nj": "o86rztxl3o0tczakkq0w9osy", "cmc8qaqof000adnwihl1ap021": "ebfupdg4sj3nrllkzlz2h1lp", "cmc8qbibs0001mx4jv05jzaos": "ztor4y77n0vlmi4gtpulcaeg", "cmc8qcdq60002mx4j1h5pwmmm": "cov7r4kbwwpfka9l19n06k5l", "cmc8r3w2e0005mx4jnypzqq9u": "do4ubf7hylnasadlf1sm6zl8", "cmc8r8j610002oc94g8gef210": "wcqb7oboil1kfsycm6u2l74f", "cmc8rfcqp0003oc94u0v3g8du": "ps4w0z5cvlbqundx9g6wshu4", "cmc8rqi5k0000taw1jb0bmxou": "ai047luba38suyy9iprmdwrm", "cmc8rs75n0006mx4jqb04yncw": "f0k89nddbooawq129n9qeubr", "cmc8rtop60001taw1ukriqotz": "dlo30e95j3utv1t65c5t9kg2", "cmc8rwv300007mx4jpb2t40x3": "xqwdj73zdptgikf9i1zhto4r", "cmc8t8tgf0000iu11yeu8f93j": "usvx21wcx4c4bbdcnv24fovr", "cmc8t9qku0001iu11en1m8mll": "m3pht8372txdw9dd1jn8e1yc", "cmc8tc5kk0000zrce6u2cn05r": "aisf5mwm171jt8of5rri2rs5", "cmc8tdrbq0001zrcep0i0rs0w": "ejlj1zs6e9yn65u9djigk6sj", "cmc8wchg40000no9uvxo4o659": "tzderakwo6g9v849m739uymv", "cmc8weuuh0001no9u31vbxjd2": "aayuuhohxtf5in01tl76qdqj", "cmc8wlczz0004no9upvc01b0t": "qg58oa6jamy98exgpepjasig", "cmc8wmkx20000owpvey6wqpni": "eivdjbevn0glf4q02h6lxpmf", "cmc8wsmq80006no9u1px001q0": "qkjy8gkyk7dcsfc4jsxl5a9q", "cmc8x4hhc0001ueawv9e9awnu": "cy8l0fcmgmqzpobdihyci06n", "cmc8x6uw10002m55qeulkal8b": "bd7uweivrzz08j34ddgqh2k3", "cmc8xag8w0003m55qf58aqbwh": "vwr6yxlwnkfp37qt5qpcau6z", "cmc8xbhiy0002ueawqd3dtqfc": "eof2mr50esvu59x3iv75hi3b", "cmc8xc6oe0004m55q4oysuqlj": "sl6oydb8686aebnmxobn3fh2", "cmc8xsyqa0003ueaw9hcjo72h": "ahdls7qhi5ukd0waf0rcsrvo", "cmc8y3nci0000sm3k12xzh97f": "fl2b5nq64fzw8dt3cc4j4gg5", "cmc8y5lnr0008m55q8fjs6s6d": "kcy3jaahbv3c1jwt79az5e1y", "cmc8y79ic00002hcc9crvsvg9": "k51hrz4c4uwklfijea8qrivv", "cmc8y81zr0006ueaw9ni9s1ts": "be03joit36ujqiboxjsjnbwl", "cmc8y9a680007ueawsuerhcxs": "wd9vu3xrqf49hnnaiaqyy9ez", "cmc8zq7io0000hyiz0zljgth4": "dojug0v1l9rv7xs28q1m7gh0", "cmc90noiv0000sjfcrl7s7m5n": "lqy103bnm4metduwq2vb6t7n", "cmc91btea00013tz6gfkgu19u": "jobfmosoam42cbz1mpknp4ds", "cmc91s9ol0000oakip4mcfj7s": "uekumpntezc5gulquoiti3ho", "cmc92jafe0000w4x0ar7nfo2l": "tehjqmymqe51csprmvh5hiqa", "cmc92mmyx0000s63ic65p2s0b": "fh43blxv6x77o96bzxooatr5", "cmc92r51d0000g74iqp7de02k": "jmkp5da6t83cjxv3uyc2kdkh", "cmc9358rl00023tz657ab0p5k": "dn5jyx4y2wqgo66i5wbxdjqk", "cmc937yzc0001vwkay1nhs85g": "i4lc7u6ixo4oj8s2r6seyh8y", "cmc93azpd0000yhhp1y300v0y": "syt6i0j0n0hyohoflbadxcx1", "cmc93b1ol0004vwka4qiwm2rr": "mzbgq223679q5mllgy8y9nr3", "cmc93jq6b0002i0tnxmjvbdz3": "x0jwqlbjzt8olpvo2b1vbid8", "cmc93vtly00073tz6gyitbmwq": "x9tvluw3p87bzxbu4l2zynwz", "cmc93yccq00093tz61mkycgx4": "va2zp5j4pfe2u8a5j730xeq7", "cmc94ex580001arf9ohjf40xw": "jm8y828o4rogcfn5udfpjjkj", "cmc94it6v00002i1lm9c336xb": "r8zazb605292ln8tub5lbekn", "cmc95v3c100032i1lq2ge4igk": "ao72cuzrzrbmgvc9jk6uvb3k", "cmc96zmne0000tx83cina54tc": "fvfdix1n6d3iydgmsuaqx2tb", "cmc985iyw0003121sz4re1xe1": "tw3q79dbi0ppg971hpogd2mr", "cmc98as4a0004121siuoh6oe4": "wt769h4iyn914ttntpf4dc5v", "cmc98cqg20000bfnz5r2oqsjp": "ajmrv2bs8ozecyb2wwnwy8vt", "cmc98fj7z0005121s4hhz8x5h": "hmbry4plk9knqlpidz7is3y4", "cmc98jz1p000c2i1lus28fr1m": "nv5xmtdcotutemgy8zehdho0", "cmc9zsocw00014irp9310x2b1": "ofjf7k1qjny5fk1m37gbdvih", "cmc9zupqt00024irp0rr8vxll": "tlc1ehdgfxam0qb8y3oex2mg", "cmca0eee700044irp6lf2a9xk": "qpb3ovwc8b9f4t7n280xbcdv", "cmca0ks9n0003ycmm0cxxxdpm": "xcvgze1b1x1pvnxit59jir7k", "cmca1dxyc000017ijosru3ptl": "duc5ti68gnm356iq8ovhtg1c", "cmca1vc9e0000527fitysfxxj": "edlp9b0culrj0cayarnxeqf1", "cmca1wmhh0001527f61yg3n8q": "wh756u8uh977qlpzzeclezeu", "cmca1xfkm0002527fm0eh00jw": "jmjtavn0hkuzam833utfmxtn", "cmca23j7i0003527fvlmfx617": "wz5dgfd6gbygu9eyeuhz4nxp", "cmca24pvc000111z0wcml1zpq": "jl5jkavn0tbs1lhgdu8qfstu", "cmca2nj0l000411z0lltyshy8": "c6py29b3bt2d1yqf2902unc2", "cmca2sqli000511z0znuork20": "wgddapywlkszuk5p17fitcx2", "cmca2ywhl0000ypbidqep2e1w": "moc62nkow58bjueov0yrash8", "cmca344uu0001ypbii7ggrsw9": "fz1v599srq4qo275oykpf86j", "cmca35cq2000611z05thpjjzh": "k4dmo7j2n9u9nfdehnbvh9z5", "cmca36dxn0002ypbi5shwd1jf": "m3fmwr423osswyj2lity94hw", "cmca3aflm0003ypbi1vcai8vc": "zzf1pwuggju68xjyd6iguiww", "cmca3eybb000711z03uf9fw7l": "zz0584nmyyzulo8av91ar94q", "cmca3p7e8000811z0zhw82stf": "iz9rhuq3e8iwm3k49sns8eig", "cmca3pb1z000911z0cw5kyivu": "hrrqb7zq94rhu1z4zl9d2w7u", "cmca3rb1e0004ypbis44nt0r4": "gtf26nocekajifwhgtv0l3hu", "cmca453ak000b11z0uei57ryt": "sfgq779cz1z6z5jzrh9huzva", "cmca4a7ki000c11z0fx7wgfpu": "jqlycnri32zhn1jn0rarowk6", "cmca4fs9r000d11z0mbc716tc": "y918trvr3u6cn65xh9qqy3k5", "cmca4g8uo0007ypbi3bl7hfls": "amr4wzn4tpe6q4da0fs909e0", "cmca4luhm0008ypbivwxh74xw": "xurdv2311ig5urzznesqlyh1", "cmca4om5m0000wsb3c6ko5log": "ken2fgbaw6415nnrshua8oqf", "cmca4qliq0001wsb34v8fvl5y": "brte7fa6649331t5q0anjq4q", "cmca4zdqj000f11z0lt7can8e": "d8d8kl33mho6cex8jxf5lqxq", "cmca50peq0003wsb37pkcox7d": "sbdvf61liytze1bywflorhuc", "cmca53tn7000g11z036j6xtqb": "lzw0qjjkyns8as9abbvug0a0", "cmca5cfq00006wsb3fsws02m6": "f1mc2z5xpfznuer7gbf9m2z5", "cmca5dezw000h11z060sp0tsa": "jd1km1finlo2wdzmxz2e2hpk", "cmca5mv9z0008wsb3k7v7iolj": "bgj9pk8ki2rsr2f9ml5fojqa", "cmca5w36g000i11z0mebsckuw": "qje44xovzsl0zhp5ka1if7ru", "cmca5zp390009wsb3ns057oi2": "ebilr9517vepocxqxw71jb1s", "cmca634zc000j11z069cxph0b": "a5n0sn291bc7h16tvw5v8j1e", "cmca74wkf0002h9ucv0963r03": "ph06qdxpq2lq6y5h93billpx", "cmca8z3400000deerdoxbvbng": "e1kuhguvdcv6ir0h3ekg09x0", "cmca9i1440000maug3h0e1apz": "wi1npgse1i792mevz1s1bqxk", "cmca9lltx0002deershyskqym": "pwvlmog9dj810lk0uzzs5hg7", "cmca9maw40001maugvzyxfzb5": "jhkaz1fwq8khpkxm5qpkp2in", "cmca9xgj20001vbm2g17ad6gw": "c1mfljhe4xp6cfsb3l4aaj2f", "cmca9ywn50002vbm2g1fmk4jy": "gl8jemaxr838s1b6xkoxcmgn", "cmcaa0dpi0003vbm2iz3g0xi1": "pkvan83oww6db6aia21yn06a", "cmcaajs990003deerm1w0aq0x": "qn3ckm7f4l447snrrktwnsvs", "cmcaalse40007vbm2znpl78a1": "t7n7t2r6lputrac5f8zpltnn", "cmcaaneij0008vbm28naseecr": "strkggvonxxz75du80whigtl", "cmcaaoaam0003maugpzdy4gco": "tjwrwq6up2n2rkxyliot57m0", "cmcaayv0g0000lf2exqi9bul0": "qadvj087et0byzw92l6sx267", "cmcab81ss0005deerxdcnn3r1": "bgydpsv728sja4udeqc2qvsa", "cmcabd9ep0001lf2eigjgl9eh": "bb6ifkszlm41qz7v4m5ugq2x", "cmcabforl0000pcwptbbzg0hd": "mlsp4m827vuew67g4b7t4n7y", "cmcabit0k0006deer2xdm32az": "pjelg1049r7m0exholtzk8uu", "cmcabs2ti0001pcwpvyv1khfl": "zv5u0onvcoon3fe2tjwmd4a7", "cmcabuqou0003pcwpek65bp0l": "hanm1t98epkhsvj6qvcxhb0v", "cmcabyn0x0006maugbjvy2epj": "aheusrdw6j8qjoufzmpz992u", "cmcac83vv0007maugdlyykyvl": "ctkpx8st1b08j1ml9iwefj06", "cmcacalfm0004pcwpm7gvw7ju": "t6i2r3zmz4vc8p36l4532kye", "cmcadnqrg0000lwac19e9ki2g": "mn7cspr5908ogs6tfssojsob", "cmcadwxgv000060odd97psprj": "bt23acxwlkvzkobhqmkupdib", "cmcae0g650001lwac88wn2ehi": "h03u6ic17zn2akexxw4bi6vf", "cmcaf5m9r0000csf9jaype3mt": "qt4lvwb2xkcsphf310ipe6a0", "cmcahwv7j0000pg8yy0l80i8n": "eisoxzueix1o1ap5b1wfj7yn", "cmcahz61w0001pg8yvtnt40v9": "a6pwkctn785lfeiz4f2le4rn", "cmcaibv5g00003g74cqvqxx0a": "ggjtz210upvgf8lpz1f5xg4u", "cmcaicay100023g74uucy4a2t": "g3gb3iv24fu738qz6s1vb776", "cmcaid0j600033g742pc3uctj": "ij0ohu0a8ec246m99n66dvzn", "cmcakvcbh0000sugu3gswz9ju": "r0jkv0jlj7bf852dm32hs0r1", "cmcbeq80r00006jl2wbxyr65f": "c56nlrafqai2fc9ks03peanz", "cmcbergv10001cleyrmcfc5xz": "ul6qeuh7vtbfww01ieirnzxy", "cmcbesozm0000v4a5hbgs30gb": "rqpw4r6p0gire2l96iqbmfxv", "cmcbu0qk00000msxzrb3x3vyk": "nkhlltrihrpz68kqja72fh1k", "cmcbvmk8n00014afuogoixd9w": "gqklvdq9urtq0ffio6tp2k84", "cmcbvok8b00001p5dzo14elze": "wtx9qgcal76c1mjszfu9iupv", "cmcbvpj2u000168hlhtwyfhv1": "q3b0z883d4mzr7i5q5gij019", "cmcbvqpup0001msxzzj5k8qpl": "g6mgar65bawrp2ipodsa1llm", "cmcbw8ldv00011p5d23pr40ea": "w8t215aanmdhp68dy8s4nbzv", "cmcbwgl2w000368hlwe7fg83a": "afjxm2td8wap68r30uj4bv7t", "cmcbx490w00021p5d5efbgydl": "fu4qn9kdamd9qjfvrqdzhyl8", "cmcbxf1320000dbbnfssvs4gc": "iykqlle9kourc0i1wg8h086o", "cmcbywuhf0001rejg0eke1tgo": "jfr0i5xgya3kygg2qvqdip2p", "cmcbz0vt5000012dao3zvjhbx": "pzj0k5hv3rf0sn97mcbn4ow4", "cmcc0hk9a0000w1mc8858xmrp": "c6xjs3pvn2d19aliekeprwcz", "cmcc0igf20002pitzrvfngyg5": "o7z76zaakazpmdaeep5wz5zp", "cmcc0pjrr0000enrfz68qvp1y": "u30qsxxb7nbcjrx9ob1ngvbg", "cmcc0td520001enrf25lha5fu": "z8e8pwa6ah9woo691ealxqmf", "cmcc1bjyb00007xvnz8g94mqe": "lv8flwsd8eks8y3mjcf8m9cj", "cmcc1gbm90000zjvcucvg9yo0": "olreotscldts25dl8gbifp74", "cmcc336uv0000148magebrz6q": "aab5brx2efl6613ef19dby5d", "cmcc36dtl0001148mfjiola3m": "o8uvajo84z3cfcc7t0r2d74w", "cmcc385j60002ed5w6l6wnqxz": "qi97grgwcv65ijrsvs1dxrox", "cmccxuhwf0005ohka3mvbzty6": "qw66z644mu92jztv2a6bqzoc", "cmcef250r0000ubkzceftz78j": "u043sab63qdguek2fhxzoj6h", "cmcelaac50001jcppxgk2j1dc": "owjdbnd0ikqb1rnv5o2jxvaj", "cmcelemro0001outto4fyi8qq": "gkuy4lj2g5x9ofpej9ceax6g", "cmcelh22j0002jcpp66cjqrt8": "a1m1w5hw5dqc7dr8n6jj8pxh", "cmceowskk0000s3q2pmttd9nr": "ntpbqjy2kb7jg18apl1ct29y", "cmcew3owk00006dgfxjdxsdes": "k9866ymwir38axmfuoi686oi", "cmcfs3rgj00001467arl83dc3": "qtj8t46x96ovv0z2yjc34af0", "cmcfsgv5v000012wn88m5yojy": "jkqx3zf7yubi3ae0k8cx7fh9", "cmcftfbyj000212wnw998pcij": "snr22ndprfd1avfmkjfhlvoj", "cmcfupk8100002nes3zhrrupy": "q3ewinqapxrnc5kgjgl2zafc", "cmcfut6up00012nesj7o2k4jt": "sqnyr9toxd86pfk3323ah9iv", "cmcisxrfo0000lro80il77cwo": "sws9io7stey6fr7l5x3yw66f", "cmcit17xo0001lro8ljyvvx5k": "s17m5wvwikyozkwl6xsbnkd5", "cmcj41xg10002uw6rqdo5487l": "w6egvuyl73mw7hm1vllkxd0w", "cmck46ogl000014j6f6tjlksb": "ci8al8dli7j2l5582x5hw9un", "cmclj29bd0000kk95mp22u1ad": "wlb1h57b41jozjtpbsvuxy4x", "cmcljxmmc0004vlyko6bk6jtp": "v0osebb6vo3pzdvlipjkasx7", "cmcll7yuo0000oynqigt893f5": "lcsd8bmb8dxp7tmn2f0zes7j", "cmcob5a4q000196w6kshj21ju": "bsj99v48oes4pogt4qszqnkf", "cmcoc6wgn0000i6yfez1h4uts": "wg02kmi94zpspubcb1omu1o1", "cmcocefn00001760iu7evhss8": "kx3esfb1bjy6wrwrew1uwnay", "cmcoczuf80000m62povh3qgag": "kbcpw22zl2cdztwioktm7edl", "cmcprj22u0001qghfhsjwkvoe": "mk9c3pqw2shuk7lj1825kqqv", "cmcsoanhk0000ka6c8do81y5m": "q61748eje72bpy3i3z3nh2ch", "cmcsur5230000ww77n9zp4hss": "pmkzgjpzl0izyn0g9suleymz", "cmcwwu9v30000yknqrmo4dhra": "wdnwpc6xdihvy59qy4oaq5f9", "cmcx5u6ua00008okfrhpes7o3": "n3bavqwngmqr7irtqpm141wi", "cmcx7ytyl0000q4uxxytyc0w4": "h20mzf3q8yjfdioirwx0o6qo", "cmcyeiijc0000137zm3lavf12": "gsxjk5x6i69yzrermzxv41uh", "cmcyu6p8f0000uhzjiky1d13w": "m1p0qqhb2qpglpiay7hta2at", "cmcyu7vxu00019v2si9kqinvv": "g1js7imc5eyxshx7s7ckcm78", "cmcztzki70000xwgvd3yodef4": "zlmu1yq41d9p56bjsi19v9n1", "cmczvp1ao0002p4vn5bupbkma": "fe3v0qd662o7r4j3jow79pfz", "cmczvwrjp0000dwru993lndii": "x68dfg7fn8qzmgol51dwvd0j", "cmd08u4570000pe94rta19ced": "prr0xbbmfmz4sm43dx68zx7j", "cmd0a73zs000010xhhxhdpf8r": "swk6xhu1cbx0t0mj2vlowenh"}}}