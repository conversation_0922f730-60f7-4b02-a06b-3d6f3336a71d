import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const templateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  content: z.string().min(1, 'Template content is required'),
  type: z.enum(['SMS', 'EMAIL', 'GENERAL']).default('GENERAL'),
  category: z.enum(['ENROLLMENT', 'PAYMENT', 'REMINDER', 'COMPLETION', 'ATTENDANCE', 'GENERAL']).default('GENERAL'),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const category = searchParams.get('category')

    // Build where clause
    const where: any = {}
    if (type) where.type = type
    if (category) where.category = category

    // Get templates from database
    const dbTemplates = await prisma.communicationTemplate.findMany({
      where,
      orderBy: { createdAt: 'desc' },
    })

    // Add default templates if no custom templates exist
    const defaultTemplates = [
      {
        id: 'default-enrollment',
        name: 'Enrollment Confirmation',
        content: 'Assalomu alaykum {studentName}! Siz {courseName} kursiga muvaffaqiyatli ro\'yxatdan o\'tdingiz. Darslar {startDate} dan boshlanadi. Innovative Centre',
        type: 'SMS',
        category: 'ENROLLMENT',
        isDefault: true,
      },
      {
        id: 'default-payment',
        name: 'Payment Confirmation',
        content: '{studentName}, {amount} so\'m to\'lov qabul qilindi. Kurs: {courseName}. Rahmat! Innovative Centre',
        type: 'SMS',
        category: 'PAYMENT',
        isDefault: true,
      },
      {
        id: 'default-reminder',
        name: 'Class Reminder',
        content: '{studentName}, bugun {time} da {courseName} darsi bor. Kechikmaslik uchun iltimos! Innovative Centre',
        type: 'SMS',
        category: 'REMINDER',
        isDefault: true,
      },
      {
        id: 'default-completion',
        name: 'Course Completion',
        content: 'Tabriklaymiz {studentName}! Siz {courseName} kursini muvaffaqiyatli yakunladingiz. {nextLevel} darajasiga o\'tishingiz mumkin. Innovative Centre',
        type: 'SMS',
        category: 'COMPLETION',
        isDefault: true,
      },
      {
        id: 'default-attendance',
        name: 'Attendance Alert',
        content: 'Hurmatli {parentName}, {studentName} bugun {courseName} darsida qatnashmadi. Innovative Centre',
        type: 'SMS',
        category: 'ATTENDANCE',
        isDefault: true,
      },
      {
        id: 'default-general',
        name: 'General Message',
        content: 'Assalomu alaykum! {message} Innovative Centre',
        type: 'GENERAL',
        category: 'GENERAL',
        isDefault: true,
      },
    ]

    // Combine database templates with default templates
    const allTemplates = [
      ...dbTemplates.map(template => ({
        id: template.id,
        name: template.name,
        content: template.content,
        type: template.type,
        category: template.category,
        isDefault: false,
      })),
      ...defaultTemplates.filter(defaultTemplate => {
        // Only include default templates if no custom template exists for that category
        return !dbTemplates.some(dbTemplate => 
          dbTemplate.category === defaultTemplate.category && 
          dbTemplate.type === defaultTemplate.type
        )
      }),
    ]

    return NextResponse.json({
      templates: allTemplates,
      count: allTemplates.length,
    })
  } catch (error) {
    // Log error only in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error fetching communication templates:', error)
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to create templates (ADMIN or MANAGER)
    const userRole = (session.user as any).role
    if (!['ADMIN', 'MANAGER'].includes(userRole)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = templateSchema.parse(body)

    const template = await prisma.communicationTemplate.create({
      data: {
        ...validatedData,
        createdBy: session.user.id,
      },
    })

    return NextResponse.json({
      template: {
        id: template.id,
        name: template.name,
        content: template.content,
        type: template.type,
        category: template.category,
        isDefault: false,
      },
    }, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    // Log error only in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error creating communication template:', error)
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
