import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const updateGroupSchema = z.object({
  name: z.string().min(1).optional(),
  courseId: z.string().optional(),
  teacherId: z.string().optional(),
  capacity: z.number().min(1).max(50).optional(),
  schedule: z.string().optional(),
  room: z.string().optional(),
  cabinetId: z.string().optional(),
  branch: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  isActive: z.boolean().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const group = await prisma.group.findUnique({
      where: { id },
      include: {
        course: {
          select: {
            id: true,
            name: true,
            level: true,
            description: true,
            duration: true,
            price: true,
          },
        },
        teacher: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true,
              },
            },
          },
        },
        enrollments: {
          include: {
            student: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true,
                level: true,
                branch: true,
                status: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        classes: {
          include: {
            teacher: {
              include: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            _count: {
              select: {
                attendances: true,
              },
            },
          },
          orderBy: { date: 'desc' },
          take: 10,
        },
        _count: {
          select: {
            enrollments: true,
            classes: true,
          },
        },
      },
    })

    if (!group) {
      return NextResponse.json(
        { error: 'Group not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(group)
  } catch (error) {
    console.error('Error fetching group:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const validatedData = updateGroupSchema.parse(body)

    // Check if group exists
    const existingGroup = await prisma.group.findUnique({
      where: { id },
    })

    if (!existingGroup) {
      return NextResponse.json(
        { error: 'Group not found' },
        { status: 404 }
      )
    }

    // Validate course and teacher exist if provided
    if (validatedData.courseId) {
      const course = await prisma.course.findUnique({
        where: { id: validatedData.courseId },
      })
      if (!course) {
        return NextResponse.json(
          { error: 'Course not found' },
          { status: 400 }
        )
      }
    }

    if (validatedData.teacherId) {
      const teacher = await prisma.teacher.findUnique({
        where: { id: validatedData.teacherId },
      })
      if (!teacher) {
        return NextResponse.json(
          { error: 'Teacher not found' },
          { status: 400 }
        )
      }
    }

    const group = await prisma.group.update({
      where: { id },
      data: {
        ...validatedData,
        startDate: validatedData.startDate ? new Date(validatedData.startDate) : undefined,
        endDate: validatedData.endDate ? new Date(validatedData.endDate) : undefined,
        updatedAt: new Date(),
      },
      include: {
        course: {
          select: {
            name: true,
            level: true,
          },
        },
        teacher: {
          select: {
            id: true,
            tier: true,
            subject: true,
            user: {
              select: {
                name: true,
              },
            },
          },
        },
        _count: {
          select: {
            enrollments: true,
          },
        },
      },
    })

    return NextResponse.json(group)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating group:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    // Check if group exists
    const existingGroup = await prisma.group.findUnique({
      where: { id },
      include: {
        enrollments: true,
      },
    })

    if (!existingGroup) {
      return NextResponse.json(
        { error: 'Group not found' },
        { status: 404 }
      )
    }

    // Check if group has active enrollments
    const activeEnrollments = existingGroup.enrollments.filter(
      enrollment => enrollment.status === 'ACTIVE'
    )

    if (activeEnrollments.length > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete group with active enrollments',
          details: `Group has ${activeEnrollments.length} active enrollment(s)`
        },
        { status: 400 }
      )
    }



    // Delete group
    await prisma.group.delete({
      where: { id },
    })

    return NextResponse.json({
      message: 'Group deleted successfully',
      deletedId: id
    })
  } catch (error) {
    console.error('Error deleting group:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
