'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Users, DollarSign, TrendingUp, Award, Calendar, Target } from 'lucide-react'

interface Teacher {
  id: string
  name: string
  email: string
}

interface TeacherKPIs {
  teacher: Teacher
  period: string
  kpis: {
    newStudents: {
      count: number
      label: string
    }
    payments: {
      totalAmount: number
      count: number
      label: string
    }
    retention: {
      rate: number
      activeStudents: number
      totalStudents: number
      label: string
    }
    progress: {
      rate: number
      studentsWithProgress: number
      totalStudents: number
      label: string
    }
    testPerformance: {
      averageScore: number
      testsCompleted: number
      label: string
    }
    classActivity: {
      classesHeld: number
      label: string
    }
  }
}

export default function TeacherKPIsPage() {
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [teacherKPIs, setTeacherKPIs] = useState<TeacherKPIs[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('30')

  const fetchTeachers = async () => {
    try {
      const response = await fetch('/api/teachers?limit=1000')
      if (!response.ok) throw new Error('Failed to fetch teachers')

      const data = await response.json()
      setTeachers(data.teachers || [])
    } catch (error) {
      console.error('Error fetching teachers:', error)
    }
  }

  const fetchAllTeacherKPIs = useCallback(async () => {
    setLoading(true)
    try {
      const kpiPromises = teachers.map(async (teacher) => {
        const response = await fetch(`/api/teachers/${teacher.id}/kpis?period=${selectedPeriod}`)
        if (response.ok) {
          return await response.json()
        }
        return null
      })

      const results = await Promise.all(kpiPromises)
      setTeacherKPIs(results.filter(Boolean))
    } catch (error) {
      console.error('Error fetching teacher KPIs:', error)
    } finally {
      setLoading(false)
    }
  }, [teachers, selectedPeriod])

  useEffect(() => {
    fetchTeachers()
  }, [])

  useEffect(() => {
    if (teachers.length > 0) {
      fetchAllTeacherKPIs()
    }
  }, [teachers, selectedPeriod, fetchAllTeacherKPIs])

  const getPerformanceColor = (value: number, type: 'percentage' | 'score') => {
    if (type === 'percentage') {
      if (value >= 80) return 'text-green-600'
      if (value >= 60) return 'text-yellow-600'
      return 'text-red-600'
    } else {
      if (value >= 85) return 'text-green-600'
      if (value >= 70) return 'text-yellow-600'
      return 'text-red-600'
    }
  }

  const getPerformanceBadge = (value: number, type: 'percentage' | 'score') => {
    if (type === 'percentage') {
      if (value >= 80) return { label: 'Excellent', variant: 'default' as const }
      if (value >= 60) return { label: 'Good', variant: 'secondary' as const }
      return { label: 'Needs Improvement', variant: 'destructive' as const }
    } else {
      if (value >= 85) return { label: 'Excellent', variant: 'default' as const }
      if (value >= 70) return { label: 'Good', variant: 'secondary' as const }
      return { label: 'Needs Improvement', variant: 'destructive' as const }
    }
  }

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading teacher KPIs...</div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Teacher KPIs</h1>
          <p className="text-muted-foreground">
            Monitor teacher performance metrics and key indicators
          </p>
        </div>
        <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7">Last 7 days</SelectItem>
            <SelectItem value="30">Last 30 days</SelectItem>
            <SelectItem value="90">Last 90 days</SelectItem>
            <SelectItem value="365">Last year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Overall Summary */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Teachers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{teachers.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total New Students</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {teacherKPIs.reduce((sum, kpi) => sum + kpi.kpis.newStudents.count, 0)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${teacherKPIs.reduce((sum, kpi) => sum + kpi.kpis.payments.totalAmount, 0).toLocaleString()}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Retention Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {teacherKPIs.length > 0 
                ? Math.round(teacherKPIs.reduce((sum, kpi) => sum + kpi.kpis.retention.rate, 0) / teacherKPIs.length)
                : 0}%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Individual Teacher KPIs */}
      <div className="space-y-6">
        {teacherKPIs.map((teacherData) => (
          <Card key={teacherData.teacher.id} className="overflow-hidden">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">{teacherData.teacher.name}</CardTitle>
                  <CardDescription>{teacherData.teacher.email}</CardDescription>
                </div>
                <Badge variant="outline">{teacherData.period}</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {/* New Students */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">New Students</span>
                  </div>
                  <div className="text-2xl font-bold">{teacherData.kpis.newStudents.count}</div>
                  <p className="text-xs text-muted-foreground">Students acquired</p>
                </div>

                {/* Payments */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium">Revenue Generated</span>
                  </div>
                  <div className="text-2xl font-bold">${teacherData.kpis.payments.totalAmount.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">{teacherData.kpis.payments.count} payments</p>
                </div>

                {/* Retention Rate */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-purple-500" />
                    <span className="text-sm font-medium">Retention Rate</span>
                  </div>
                  <div className={`text-2xl font-bold ${getPerformanceColor(teacherData.kpis.retention.rate, 'percentage')}`}>
                    {teacherData.kpis.retention.rate}%
                  </div>
                  <div className="flex items-center gap-2">
                    <Progress value={teacherData.kpis.retention.rate} className="flex-1" />
                    <Badge {...getPerformanceBadge(teacherData.kpis.retention.rate, 'percentage')}>
                      {getPerformanceBadge(teacherData.kpis.retention.rate, 'percentage').label}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {teacherData.kpis.retention.activeStudents} of {teacherData.kpis.retention.totalStudents} students active
                  </p>
                </div>

                {/* Progress Rate */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-orange-500" />
                    <span className="text-sm font-medium">Progress Rate</span>
                  </div>
                  <div className={`text-2xl font-bold ${getPerformanceColor(teacherData.kpis.progress.rate, 'percentage')}`}>
                    {teacherData.kpis.progress.rate}%
                  </div>
                  <div className="flex items-center gap-2">
                    <Progress value={teacherData.kpis.progress.rate} className="flex-1" />
                    <Badge {...getPerformanceBadge(teacherData.kpis.progress.rate, 'percentage')}>
                      {getPerformanceBadge(teacherData.kpis.progress.rate, 'percentage').label}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {teacherData.kpis.progress.studentsWithProgress} students advanced levels
                  </p>
                </div>

                {/* Test Performance */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Award className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm font-medium">Test Performance</span>
                  </div>
                  <div className={`text-2xl font-bold ${getPerformanceColor(teacherData.kpis.testPerformance.averageScore, 'score')}`}>
                    {teacherData.kpis.testPerformance.averageScore}%
                  </div>
                  <div className="flex items-center gap-2">
                    <Progress value={teacherData.kpis.testPerformance.averageScore} className="flex-1" />
                    <Badge {...getPerformanceBadge(teacherData.kpis.testPerformance.averageScore, 'score')}>
                      {getPerformanceBadge(teacherData.kpis.testPerformance.averageScore, 'score').label}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {teacherData.kpis.testPerformance.testsCompleted} tests completed
                  </p>
                </div>

                {/* Class Activity */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-indigo-500" />
                    <span className="text-sm font-medium">Classes Held</span>
                  </div>
                  <div className="text-2xl font-bold">{teacherData.kpis.classActivity.classesHeld}</div>
                  <p className="text-xs text-muted-foreground">Classes conducted</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {teacherKPIs.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No teacher KPI data available for the selected period.</p>
        </div>
      )}
    </div>
  )
}
