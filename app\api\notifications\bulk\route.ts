import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const bulkUpdateSchema = z.object({
  action: z.enum(['mark_read', 'mark_unread', 'delete']),
  notificationIds: z.array(z.string()).optional(),
  all: z.boolean().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action, notificationIds, all } = bulkUpdateSchema.parse(body)

    let where: any = { userId: session.user.id }

    // If not all notifications, filter by specific IDs
    if (!all && notificationIds && notificationIds.length > 0) {
      where.id = { in: notificationIds }
    }

    let result
    switch (action) {
      case 'mark_read':
        result = await prisma.notification.updateMany({
          where,
          data: { read: true, updatedAt: new Date() },
        })
        break

      case 'mark_unread':
        result = await prisma.notification.updateMany({
          where,
          data: { read: false, updatedAt: new Date() },
        })
        break

      case 'delete':
        result = await prisma.notification.deleteMany({
          where,
        })
        break

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

    // Get updated unread count
    const unreadCount = await prisma.notification.count({
      where: { userId: session.user.id, read: false }
    })

    return NextResponse.json({
      success: true,
      affected: result.count,
      unreadCount,
      message: `${action.replace('_', ' ')} operation completed successfully`,
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error in bulk notification operation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
