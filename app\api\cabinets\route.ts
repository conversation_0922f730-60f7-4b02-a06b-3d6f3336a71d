import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const cabinetSchema = z.object({
  name: z.string().min(1, 'Cabinet name is required'),
  number: z.string().min(1, 'Cabinet number is required'),
  capacity: z.number().min(1, 'Capacity must be at least 1').max(100, 'Capacity cannot exceed 100'),
  floor: z.number().optional(),
  building: z.string().optional(),
  branch: z.string().min(1, 'Branch is required'),
  equipment: z.string().optional(),
  notes: z.string().optional(),
  isActive: z.boolean().default(true),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search')
    const branch = searchParams.get('branch')
    const isActive = searchParams.get('isActive')

    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { number: { contains: search, mode: 'insensitive' } },
        { building: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (branch) {
      // Use branch ID directly for database query (cabinets are stored with branch IDs, not names)
      where.branch = branch
    }

    if (isActive !== null) {
      where.isActive = isActive === 'true'
    }

    const skip = (page - 1) * limit

    const [cabinets, total] = await Promise.all([
      prisma.cabinet.findMany({
        where,
        include: {
          groups: {
            include: {
              course: {
                select: {
                  name: true,
                  level: true,
                },
              },
              teacher: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
          _count: {
            select: {
              groups: true,
              schedules: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.cabinet.count({ where }),
    ])

    return NextResponse.json({
      cabinets,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching cabinets:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to create cabinets
    if (!session.user.role || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = cabinetSchema.parse(body)

    // Check if cabinet number already exists in the same branch
    const existingCabinet = await prisma.cabinet.findFirst({
      where: {
        number: validatedData.number,
        branch: validatedData.branch,
      },
    })

    if (existingCabinet) {
      return NextResponse.json(
        { error: 'Cabinet number already exists in this branch' },
        { status: 400 }
      )
    }

    const cabinet = await prisma.cabinet.create({
      data: validatedData,
      include: {
        groups: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
            teacher: {
              include: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
        _count: {
          select: {
            groups: true,
            schedules: true,
          },
        },
      },
    })

    // Log activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: (session.user.role as Role) || Role.ADMIN,
      action: 'CREATE',
      resource: 'CABINET',
      resourceId: cabinet.id,
      details: `Created cabinet ${cabinet.name} (${cabinet.number})`,
    })

    return NextResponse.json(cabinet, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating cabinet:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
