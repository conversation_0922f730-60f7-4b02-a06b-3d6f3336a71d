const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createCredentials() {
  console.log('🔐 Creating test credentials...')

  try {
    // Admin (already exists)
    console.log('✅ ADMIN: +998906006299 / Parviz0106$')
    
    // Manager
    const managerPassword = await bcrypt.hash('manager123', 10)
    try {
      await prisma.user.upsert({
        where: { phone: '+998901111111' },
        update: { password: managerPassword, role: 'MANAGER' },
        create: {
          phone: '+998901111111',
          name: 'Manager User',
          email: '<EMAIL>',
          role: 'MANAGER',
          password: managerPassword,
        },
      })
      console.log('✅ MANAGER: +998901111111 / manager123')
    } catch (e) {
      console.log('⚠️ MANAGER: +998901111111 / manager123 (may already exist)')
    }

    // Teacher
    const teacherPassword = await bcrypt.hash('teacher123', 10)
    try {
      await prisma.user.upsert({
        where: { phone: '+998902222222' },
        update: { password: teacherPassword, role: 'TEACHER' },
        create: {
          phone: '+998902222222',
          name: 'Teacher User',
          email: '<EMAIL>',
          role: 'TEACHER',
          password: teacherPassword,
        },
      })
      console.log('✅ TEACHER: +998902222222 / teacher123')
    } catch (e) {
      console.log('⚠️ TEACHER: +998902222222 / teacher123 (may already exist)')
    }

    // Reception
    const receptionPassword = await bcrypt.hash('reception123', 10)
    try {
      await prisma.user.upsert({
        where: { phone: '+998903333333' },
        update: { password: receptionPassword, role: 'RECEPTION' },
        create: {
          phone: '+998903333333',
          name: 'Reception User',
          email: '<EMAIL>',
          role: 'RECEPTION',
          password: receptionPassword,
        },
      })
      console.log('✅ RECEPTION: +998903333333 / reception123')
    } catch (e) {
      console.log('⚠️ RECEPTION: +998903333333 / reception123 (may already exist)')
    }

    // Cashier
    const cashierPassword = await bcrypt.hash('cashier123', 10)
    try {
      await prisma.user.upsert({
        where: { phone: '+998904444444' },
        update: { password: cashierPassword, role: 'CASHIER' },
        create: {
          phone: '+998904444444',
          name: 'Cashier User',
          email: '<EMAIL>',
          role: 'CASHIER',
          password: cashierPassword,
        },
      })
      console.log('✅ CASHIER: +998904444444 / cashier123')
    } catch (e) {
      console.log('⚠️ CASHIER: +998904444444 / cashier123 (may already exist)')
    }

    // Academic Manager
    const academicPassword = await bcrypt.hash('academic123', 10)
    try {
      await prisma.user.upsert({
        where: { phone: '+998905555555' },
        update: { password: academicPassword, role: 'ACADEMIC_MANAGER' },
        create: {
          phone: '+998905555555',
          name: 'Academic Manager',
          email: '<EMAIL>',
          role: 'ACADEMIC_MANAGER',
          password: academicPassword,
        },
      })
      console.log('✅ ACADEMIC_MANAGER: +998905555555 / academic123')
    } catch (e) {
      console.log('⚠️ ACADEMIC_MANAGER: +998905555555 / academic123 (may already exist)')
    }

    // Student
    const studentPassword = await bcrypt.hash('student123', 10)
    try {
      await prisma.user.upsert({
        where: { phone: '+998906666666' },
        update: { password: studentPassword, role: 'STUDENT' },
        create: {
          phone: '+998906666666',
          name: 'Student User',
          email: '<EMAIL>',
          role: 'STUDENT',
          password: studentPassword,
        },
      })
      console.log('✅ STUDENT: +998906666666 / student123')
    } catch (e) {
      console.log('⚠️ STUDENT: +998906666666 / student123 (may already exist)')
    }

    console.log('\n🎉 All credentials ready!')
    console.log('\n📋 COMPLETE CREDENTIALS LIST:')
    console.log('================================')
    console.log('ADMIN            | +998906006299 | Parviz0106$')
    console.log('MANAGER          | +998901111111 | manager123')
    console.log('TEACHER          | +998902222222 | teacher123')
    console.log('RECEPTION        | +998903333333 | reception123')
    console.log('CASHIER          | +998904444444 | cashier123')
    console.log('ACADEMIC_MANAGER | +998905555555 | academic123')
    console.log('STUDENT          | +998906666666 | student123')

  } catch (error) {
    console.error('❌ Error:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

createCredentials()
