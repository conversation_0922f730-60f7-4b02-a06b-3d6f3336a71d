import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { Role } from '@prisma/client'
import { errorLogger } from '@/lib/error-logging'

export interface AuthenticatedUser {
  id: string
  name: string
  email?: string
  phone: string
  role: Role
}

export interface AuthResult {
  success: boolean
  user?: AuthenticatedUser
  error?: string
}

/**
 * Authenticate and authorize API requests
 */
export async function authenticateApiRequest(
  request: NextRequest,
  allowedRoles?: Role[]
): Promise<AuthResult> {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      logSecurityEvent('medium', 'unauthorized_access_attempt', {
        path: request.nextUrl.pathname,
        method: request.method,
        userAgent: request.headers.get('user-agent'),
        ip: getClientIP(request)
      })
      
      return {
        success: false,
        error: 'Authentication required'
      }
    }

    const user: AuthenticatedUser = {
      id: session.user.id,
      name: session.user.name || '',
      email: session.user.email || undefined,
      phone: session.user.phone || '',
      role: session.user.role as Role
    }

    // Check role authorization if roles are specified
    if (allowedRoles && allowedRoles.length > 0) {
      if (!allowedRoles.includes(user.role)) {
        logSecurityEvent('high', 'unauthorized_role_access', {
          userId: user.id,
          userRole: user.role,
          requiredRoles: allowedRoles,
          path: request.nextUrl.pathname,
          method: request.method
        })
        
        return {
          success: false,
          error: 'Insufficient permissions'
        }
      }
    }

    return {
      success: true,
      user
    }
  } catch (error) {
    errorLogger.logError(
      error instanceof Error ? error : String(error),
      undefined,
      {
        source: 'api_auth',
        path: request.nextUrl.pathname,
        method: request.method
      }
    )
    
    return {
      success: false,
      error: 'Authentication failed'
    }
  }
}

/**
 * Check if user has specific role
 */
export function hasRole(user: AuthenticatedUser, role: Role): boolean {
  return user.role === role
}

/**
 * Check if user has any of the specified roles
 */
export function hasAnyRole(user: AuthenticatedUser, roles: Role[]): boolean {
  return roles.includes(user.role)
}

/**
 * Check if user is admin
 */
export function isAdmin(user: AuthenticatedUser): boolean {
  return user.role === 'ADMIN'
}

/**
 * Check if user is manager or admin
 */
export function isManagerOrAdmin(user: AuthenticatedUser): boolean {
  return ['ADMIN', 'MANAGER'].includes(user.role)
}

/**
 * Check if user is teacher, manager, or admin
 */
export function isTeacherOrAbove(user: AuthenticatedUser): boolean {
  return ['ADMIN', 'MANAGER', 'TEACHER', 'ACADEMIC_MANAGER'].includes(user.role)
}

/**
 * Get client IP address from request
 */
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('remote-addr')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  if (remoteAddr) {
    return remoteAddr
  }
  
  return 'unknown'
}

/**
 * Log security events
 */
export function logSecurityEvent(
  level: 'low' | 'medium' | 'high' | 'critical',
  type: string,
  details: Record<string, any>
): void {
  errorLogger.logSecurityEvent(level, type, details)
}

/**
 * Rate limiting helper
 */
export class RateLimiter {
  private requests: Map<string, number[]> = new Map()
  private readonly windowMs: number
  private readonly maxRequests: number

  constructor(windowMs: number = 15 * 60 * 1000, maxRequests: number = 100) {
    this.windowMs = windowMs
    this.maxRequests = maxRequests
  }

  /**
   * Check if request is within rate limit
   */
  isAllowed(identifier: string): boolean {
    const now = Date.now()
    const windowStart = now - this.windowMs
    
    // Get existing requests for this identifier
    const requests = this.requests.get(identifier) || []
    
    // Filter out old requests
    const recentRequests = requests.filter(time => time > windowStart)
    
    // Check if under limit
    if (recentRequests.length >= this.maxRequests) {
      logSecurityEvent('medium', 'rate_limit_exceeded', {
        identifier,
        requestCount: recentRequests.length,
        maxRequests: this.maxRequests,
        windowMs: this.windowMs
      })
      return false
    }
    
    // Add current request
    recentRequests.push(now)
    this.requests.set(identifier, recentRequests)
    
    return true
  }

  /**
   * Clear old entries periodically
   */
  cleanup(): void {
    const now = Date.now()
    const windowStart = now - this.windowMs
    
    for (const [identifier, requests] of this.requests.entries()) {
      const recentRequests = requests.filter(time => time > windowStart)
      if (recentRequests.length === 0) {
        this.requests.delete(identifier)
      } else {
        this.requests.set(identifier, recentRequests)
      }
    }
  }
}

// Global rate limiter instance
export const globalRateLimiter = new RateLimiter()

// Cleanup rate limiter every 5 minutes
if (typeof window === 'undefined') {
  setInterval(() => {
    globalRateLimiter.cleanup()
  }, 5 * 60 * 1000)
}

/**
 * Middleware helper for API route protection
 */
export function withAuth(allowedRoles?: Role[]) {
  return async function(
    request: NextRequest,
    handler: (request: NextRequest, user: AuthenticatedUser) => Promise<Response>
  ): Promise<Response> {
    const authResult = await authenticateApiRequest(request, allowedRoles)
    
    if (!authResult.success || !authResult.user) {
      return new Response(
        JSON.stringify({
          error: {
            message: authResult.error || 'Authentication failed',
            code: 'UNAUTHORIZED'
          }
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }
    
    return handler(request, authResult.user)
  }
}

/**
 * Validate API key (for external integrations)
 */
export function validateApiKey(apiKey: string): boolean {
  const validApiKeys = process.env.API_KEYS?.split(',') || []
  return validApiKeys.includes(apiKey)
}

export default {
  authenticateApiRequest,
  hasRole,
  hasAnyRole,
  isAdmin,
  isManagerOrAdmin,
  isTeacherOrAbove,
  getClientIP,
  logSecurityEvent,
  RateLimiter,
  globalRateLimiter,
  withAuth,
  validateApiKey
}
