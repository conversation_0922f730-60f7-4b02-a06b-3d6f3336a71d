"use client";

import * as React from "react";
import { useSession, signOut } from "next-auth/react";
import { usePathname } from "next/navigation";
import Link from "next/link";

import { TeamSwitcher } from "@/components/team-switcher";
import { ThemeToggle } from "@/components/theme-toggle";
import { useBranch } from "@/contexts/branch-context";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/sidebar";
import {
  LayoutDashboard,
  Users,
  UserPlus,
  GraduationCap,
  BookOpen,
  CreditCard,
  BarChart3,
  Settings,
  UserCheck,
  Calendar,
  ClipboardList,
  Award,
  Target,
  TrendingUp,
  FileText,
  MessageSquare,
  Shield,
  Activity,
  PieChart,
  ClipboardCheck,
  Building,
  Building2,
  LogOut,
} from "lucide-react";

// CRM Navigation Configuration
const crmNavigation = {
  teams: [
    {
      name: "Innovative Centre",
    },
  ],
  navMain: [
    {
      title: "Dashboard",
      url: "#",
      items: [
        {
          title: "Overview",
          url: "/dashboard",
          icon: LayoutDashboard,
          roles: ['ADMIN', 'MANAGER', 'RECEPTION', 'CASHIER']
        },
      ],
    },
    {
      title: "Student Management",
      url: "#",
      items: [
        {
          title: "Leads",
          url: "/dashboard/leads",
          icon: UserPlus,
          roles: ['ADMIN', 'MANAGER', 'RECEPTION']
        },
        {
          title: "Students",
          url: "/dashboard/students",
          icon: Users,
          roles: ['ADMIN', 'MANAGER', 'RECEPTION', 'CASHIER']
        },
      ],
    },
    {
      title: "Academic Management",
      url: "#",
      items: [
        {
          title: "Teachers",
          url: "/dashboard/teachers",
          icon: UserCheck,
          roles: ['ADMIN', 'MANAGER']
        },
        {
          title: "Groups",
          url: "/dashboard/groups",
          icon: GraduationCap,
          roles: ['ADMIN', 'MANAGER', 'RECEPTION']
        },
        {
          title: "Cabinets",
          url: "/dashboard/cabinets",
          icon: Building,
          roles: ['ADMIN', 'MANAGER']
        },
      ],
    },
    {
      title: "Financial Management",
      url: "#",
      items: [
        {
          title: "Payments",
          url: "/dashboard/payments",
          icon: CreditCard,
          roles: ['ADMIN', 'CASHIER']
        },
        {
          title: "Analytics",
          url: "/dashboard/analytics",
          icon: BarChart3,
          roles: ['ADMIN']
        },
      ],
    },

    {
      title: "Communication",
      url: "#",
      items: [
        {
          title: "Messages",
          url: "/dashboard/communication",
          icon: MessageSquare,
          roles: ['ADMIN', 'MANAGER', 'RECEPTION']
        },
      ],
    },
    {
      title: "Administration",
      url: "#",
      items: [
        {
          title: "Users",
          url: "/dashboard/users",
          icon: Shield,
          roles: ['ADMIN']
        },
        {
          title: "Activity Logs",
          url: "/dashboard/admin/activity-logs",
          icon: Activity,
          roles: ['ADMIN']
        },
        {
          title: "KPIs",
          url: "/dashboard/admin/kpis",
          icon: PieChart,
          roles: ['ADMIN']
        },

      ],
    },
  ],
};

// Helper function to check if user has access to a navigation item
function hasAccess(userRole: string, allowedRoles: string[]): boolean {
  return allowedRoles.includes(userRole);
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const userRole = session?.user?.role || 'STUDENT';
  const { currentBranch, isLoading } = useBranch();

  return (
    <Sidebar collapsible="none" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={crmNavigation.teams} />

        {/* Branch Indicator for Admin Users */}
        {userRole === 'ADMIN' && !isLoading && (
          <div className="flex items-center gap-2 px-2 py-1.5 mx-2 rounded-md bg-primary/10 border border-primary/20">
            <Building className="h-3.5 w-3.5 text-primary" />
            <span className="text-xs font-medium text-primary">
              {currentBranch.name}
            </span>
          </div>
        )}
      </SidebarHeader>
      <SidebarContent>
        {crmNavigation.navMain.map((section) => {
          // Filter items based on user role
          const accessibleItems = section.items?.filter(item =>
            hasAccess(userRole, item.roles)
          );

          // Only show section if user has access to at least one item
          if (!accessibleItems || accessibleItems.length === 0) {
            return null;
          }

          return (
            <SidebarGroup key={section.title}>
              <SidebarGroupLabel>{section.title}</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {accessibleItems.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton asChild isActive={pathname === item.url}>
                        <Link href={item.url}>
                          <item.icon />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          );
        })}
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="flex items-center justify-between px-2 py-1">
              <ThemeToggle />
              <SidebarMenuButton
                size="sm"
                className="ml-auto cursor-pointer"
                onClick={() => signOut({
                  callbackUrl: "/auth/signin"
                })}
              >
                <LogOut />
                <span>Sign out</span>
              </SidebarMenuButton>
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
