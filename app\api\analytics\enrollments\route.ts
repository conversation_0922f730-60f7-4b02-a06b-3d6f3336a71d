import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const range = searchParams.get('range') || '12months'

    // Calculate date range
    const now = new Date()
    let startDate = new Date()
    
    switch (range) {
      case '6months':
        startDate.setMonth(now.getMonth() - 6)
        break
      case '12months':
        startDate.setFullYear(now.getFullYear() - 1)
        break
      case '24months':
        startDate.setFullYear(now.getFullYear() - 2)
        break
      default:
        startDate.setFullYear(now.getFullYear() - 1)
    }

    // Generate month array for the range
    const months = []
    const current = new Date(startDate)
    while (current <= now) {
      months.push({
        month: current.toLocaleDateString('en-US', { month: 'short' }),
        year: current.getFullYear(),
        monthStart: new Date(current.getFullYear(), current.getMonth(), 1),
        monthEnd: new Date(current.getFullYear(), current.getMonth() + 1, 0, 23, 59, 59)
      })
      current.setMonth(current.getMonth() + 1)
    }

    // Get monthly enrollment data
    const monthlyEnrollments = []
    let cumulativeTotal = 0

    for (const month of months) {
      // New enrollments this month
      const newEnrollments = await prisma.enrollment.count({
        where: {
          status: 'ACTIVE',
          createdAt: {
            gte: month.monthStart,
            lte: month.monthEnd,
          },
        },
      })

      // Dropouts this month (enrollments that became DROPPED)
      const dropouts = await prisma.enrollment.count({
        where: {
          status: 'DROPPED',
          updatedAt: {
            gte: month.monthStart,
            lte: month.monthEnd,
          },
        },
      })

      // Completions this month
      const completions = await prisma.enrollment.count({
        where: {
          status: 'COMPLETED',
          updatedAt: {
            gte: month.monthStart,
            lte: month.monthEnd,
          },
        },
      })

      // Total active enrollments at end of month
      const totalEnrollments = await prisma.enrollment.count({
        where: {
          status: 'ACTIVE',
          createdAt: {
            lte: month.monthEnd,
          },
        },
      })

      cumulativeTotal = totalEnrollments

      monthlyEnrollments.push({
        month: month.month,
        newEnrollments,
        totalEnrollments: cumulativeTotal,
        dropouts,
        completions,
        netGrowth: newEnrollments - dropouts,
      })
    }

    // Get course enrollment breakdown
    const courseEnrollments = await prisma.course.findMany({
      where: { isActive: true },
      include: {
        groups: {
          include: {
            enrollments: {
              where: { status: 'ACTIVE' },
            },
          },
        },
      },
    })

    const totalActiveEnrollments = await prisma.enrollment.count({
      where: { status: 'ACTIVE' },
    })

    const courseEnrollmentData = courseEnrollments.map(course => {
      const enrollments = course.groups.reduce((sum, group) => sum + group.enrollments.length, 0)
      const percentage = totalActiveEnrollments > 0 ? Math.round((enrollments / totalActiveEnrollments) * 100) : 0

      return {
        course: course.name,
        level: course.level,
        enrollments,
        percentage,
        groups: course.groups.length,
      }
    }).sort((a, b) => b.enrollments - a.enrollments)

    // Get enrollment by level breakdown
    const enrollmentsByLevel = await prisma.student.groupBy({
      by: ['level'],
      where: {
        enrollments: {
          some: { status: 'ACTIVE' }
        }
      },
      _count: { level: true },
    })

    const levelEnrollmentData = enrollmentsByLevel.map(level => ({
      level: level.level,
      count: level._count.level,
      percentage: totalActiveEnrollments > 0 ? Math.round((level._count.level / totalActiveEnrollments) * 100) : 0,
    }))

    // Get enrollment trends and patterns
    const enrollmentTrends = {
      averageMonthlyGrowth: monthlyEnrollments.length > 1 
        ? Math.round(monthlyEnrollments.reduce((sum, month) => sum + month.netGrowth, 0) / monthlyEnrollments.length)
        : 0,
      bestMonth: monthlyEnrollments.reduce((best, month) => 
        month.newEnrollments > best.newEnrollments ? month : best, monthlyEnrollments[0] || {}),
      worstMonth: monthlyEnrollments.reduce((worst, month) => 
        month.dropouts > worst.dropouts ? month : worst, monthlyEnrollments[0] || {}),
    }

    // Get recent enrollments for insights
    const recentEnrollments = await prisma.enrollment.findMany({
      where: {
        status: 'ACTIVE',
        createdAt: {
          gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
        },
      },
      include: {
        student: {
          select: { name: true, level: true }
        },
        group: {
          include: {
            course: { select: { name: true, level: true } }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
    })

    // Calculate retention metrics
    const retentionMetrics = {
      totalDropouts: monthlyEnrollments.reduce((sum, month) => sum + month.dropouts, 0),
      totalCompletions: monthlyEnrollments.reduce((sum, month) => sum + month.completions, 0),
      retentionRate: totalActiveEnrollments > 0 
        ? Math.round(((totalActiveEnrollments / (totalActiveEnrollments + monthlyEnrollments.reduce((sum, month) => sum + month.dropouts, 0))) * 100) * 10) / 10
        : 0,
    }

    const response = {
      monthlyEnrollments,
      courseEnrollments: courseEnrollmentData,
      levelEnrollments: levelEnrollmentData,
      summary: {
        totalActiveEnrollments,
        totalNewEnrollments: monthlyEnrollments.reduce((sum, month) => sum + month.newEnrollments, 0),
        totalDropouts: retentionMetrics.totalDropouts,
        totalCompletions: retentionMetrics.totalCompletions,
        netGrowth: monthlyEnrollments.reduce((sum, month) => sum + month.netGrowth, 0),
        retentionRate: retentionMetrics.retentionRate,
        averageMonthlyGrowth: enrollmentTrends.averageMonthlyGrowth,
      },
      trends: enrollmentTrends,
      recentEnrollments: recentEnrollments.map(enrollment => ({
        id: enrollment.id,
        studentName: enrollment.student?.name || 'Unknown',
        courseName: enrollment.group?.course?.name || 'Unknown Course',
        level: enrollment.group?.course?.level || 'Unknown',
        enrolledAt: enrollment.createdAt,
      })),
      dateRange: {
        start: startDate.toISOString(),
        end: now.toISOString(),
        range,
      },
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching enrollment analytics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
