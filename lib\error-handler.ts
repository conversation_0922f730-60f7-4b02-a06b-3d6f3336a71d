import { NextResponse } from 'next/server'
import { ZodError } from 'zod'
import { Prisma } from '@prisma/client'
import { errorLogger } from '@/lib/error-logging'

export interface ApiError {
  message: string
  code: string
  statusCode: number
  details?: any
}

export class AppError extends Error {
  public readonly statusCode: number
  public readonly code: string
  public readonly isOperational: boolean
  public details?: any

  constructor(message: string, statusCode: number = 500, code: string = 'INTERNAL_ERROR', isOperational: boolean = true) {
    super(message)
    this.statusCode = statusCode
    this.code = code
    this.isOperational = isOperational

    Error.captureStackTrace(this, this.constructor)
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR')
    this.details = details
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND')
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized access') {
    super(message, 401, 'UNAUTHORIZED')
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = 'Forbidden access') {
    super(message, 403, 'FORBIDDEN')
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409, 'CONFLICT')
  }
}

export function handleApiError(error: unknown): NextResponse {
  // Log error using centralized logging
  errorLogger.logError(
    error instanceof Error ? error : String(error),
    undefined,
    { errorType: error instanceof Error ? error.constructor.name : 'unknown' }
  )

  // Handle known error types
  if (error instanceof AppError) {
    return NextResponse.json(
      {
        error: {
          message: error.message,
          code: error.code,
          ...(error.details && { details: error.details })
        }
      },
      { status: error.statusCode }
    )
  }

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    return NextResponse.json(
      {
        error: {
          message: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        }
      },
      { status: 400 }
    )
  }

  // Handle Prisma errors with security logging
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    // Log database errors for security monitoring using centralized logging
    errorLogger.logSecurityEvent(
      'high',
      'database_error',
      {
        prismaCode: error.code,
        message: error.message
      }
    )

    switch (error.code) {
      case 'P2002':
        return NextResponse.json(
          {
            error: {
              message: 'A record with this information already exists',
              code: 'DUPLICATE_RECORD',
              timestamp: new Date().toISOString()
            }
          },
          { status: 409 }
        )
      case 'P2025':
        return NextResponse.json(
          {
            error: {
              message: 'Record not found',
              code: 'NOT_FOUND',
              timestamp: new Date().toISOString()
            }
          },
          { status: 404 }
        )
      case 'P2003':
        return NextResponse.json(
          {
            error: {
              message: 'Invalid reference to related record',
              code: 'INVALID_REFERENCE',
              timestamp: new Date().toISOString()
            }
          },
          { status: 400 }
        )
      default:
        // Log unknown database errors for investigation using centralized logging
        errorLogger.logSecurityEvent(
          'high',
          'unknown_database_error',
          {
            prismaCode: error.code
          }
        )

        return NextResponse.json(
          {
            error: {
              message: 'Database operation failed',
              code: 'DATABASE_ERROR',
              timestamp: new Date().toISOString()
            }
          },
          { status: 500 }
        )
    }
  }

  // Handle generic errors with enhanced security logging
  if (error instanceof Error) {
    // Log detailed error using centralized logging
    const errorId = errorLogger.logError(
      error,
      undefined,
      {
        errorType: error.constructor.name,
        source: 'api_handler'
      }
    )

    // Return generic error message to client with tracking ID
    return NextResponse.json(
      {
        error: {
          message: 'An unexpected error occurred',
          code: 'INTERNAL_ERROR',
          timestamp: new Date().toISOString(),
          errorId: process.env.NODE_ENV === 'development' ? errorId : undefined
        }
      },
      { status: 500 }
    )
  }

  // Fallback for unknown errors
  const errorId = errorLogger.logError(
    String(error),
    undefined,
    {
      errorType: 'unknown_error_type',
      source: 'api_handler'
    }
  )

  return NextResponse.json(
    {
      error: {
        message: 'An unexpected error occurred',
        code: 'UNKNOWN_ERROR',
        timestamp: new Date().toISOString(),
        errorId: process.env.NODE_ENV === 'development' ? errorId : undefined
      }
    },
    { status: 500 }
  )
}

// Async error wrapper for API routes
export function withErrorHandler<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | NextResponse> => {
    try {
      return await handler(...args)
    } catch (error) {
      return handleApiError(error)
    }
  }
}

// Client-side error handler
export function handleClientError(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  
  if (typeof error === 'string') {
    return error
  }
  
  return 'An unexpected error occurred'
}

const errorHandler = {
  AppError,
  ValidationError,
  NotFoundError,
  UnauthorizedError,
  ForbiddenError,
  ConflictError,
  handleApiError,
  withErrorHandler,
  handleClientError
}

export default errorHandler
