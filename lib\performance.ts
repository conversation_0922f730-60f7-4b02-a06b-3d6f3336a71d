// Performance optimization utilities for Innovative Centre CRM
import { prisma } from './prisma'

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

class CacheManager {
  private cache = new Map<string, CacheEntry<any>>()
  private defaultTTL = 5 * 60 * 1000 // 5 minutes

  set<T>(key: string, data: T, ttl?: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
    })
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    return entry.data
  }

  delete(key: string): void {
    this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  // Clean expired entries
  cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

// Singleton cache instance
const cache = new CacheManager()

// Database query optimization utilities
export class QueryOptimizer {
  // Cached queries for frequently accessed data
  static async getCachedStudents(page = 1, limit = 10, search = '') {
    const cacheKey = `students:${page}:${limit}:${search}`
    const cached = cache.get(cacheKey)
    if (cached) return cached

    const skip = (page - 1) * limit
    const whereClause: any = {}

    if (search) {
      whereClause.OR = [
        { user: { name: { contains: search, mode: 'insensitive' } } },
        { user: { phone: { contains: search } } },
        { user: { email: { contains: search, mode: 'insensitive' } } },
      ]
    }

    const [students, total] = await Promise.all([
      prisma.student.findMany({
        where: whereClause,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true,
            },
          },
          enrollments: {
            select: {
              id: true,
              status: true,
              group: {
                select: {
                  name: true,
                  course: {
                    select: {
                      name: true,
                      level: true,
                    },
                  },
                },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.student.count({ where: whereClause }),
    ])

    const result = {
      students,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    }

    cache.set(cacheKey, result, 2 * 60 * 1000) // Cache for 2 minutes
    return result
  }

  static async getCachedTeachers() {
    const cacheKey = 'teachers:all'
    const cached = cache.get(cacheKey)
    if (cached) return cached

    const teachers = await prisma.teacher.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
          },
        },
        groups: {
          select: {
            id: true,
            name: true,
            course: {
              select: {
                name: true,
                level: true,
              },
            },
            enrollments: {
              select: {
                id: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    })

    cache.set(cacheKey, teachers, 5 * 60 * 1000) // Cache for 5 minutes
    return teachers
  }

  static async getCachedGroups() {
    const cacheKey = 'groups:all'
    const cached = cache.get(cacheKey)
    if (cached) return cached

    const groups = await prisma.group.findMany({
      include: {
        course: {
          select: {
            id: true,
            name: true,
            level: true,
            price: true,
          },
        },
        teacher: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        enrollments: {
          select: {
            id: true,
            status: true,
            student: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    })

    cache.set(cacheKey, groups, 3 * 60 * 1000) // Cache for 3 minutes
    return groups
  }

  static async getCachedAnalytics(timeRange = '6months') {
    const cacheKey = `analytics:${timeRange}`
    const cached = cache.get(cacheKey)
    if (cached) return cached

    // Calculate date range
    const endDate = new Date()
    const startDate = new Date()
    
    switch (timeRange) {
      case '1month':
        startDate.setMonth(endDate.getMonth() - 1)
        break
      case '3months':
        startDate.setMonth(endDate.getMonth() - 3)
        break
      case '6months':
        startDate.setMonth(endDate.getMonth() - 6)
        break
      case '1year':
        startDate.setFullYear(endDate.getFullYear() - 1)
        break
    }

    const [
      totalStudents,
      totalTeachers,
      totalGroups,
      totalCourses,
      totalRevenue,
      recentEnrollments,
      recentPayments,
    ] = await Promise.all([
      prisma.student.count(),
      prisma.teacher.count(),
      prisma.group.count(),
      prisma.course.count(),
      prisma.payment.aggregate({
        where: {
          status: 'PAID',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _sum: { amount: true },
      }),
      prisma.enrollment.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
      prisma.payment.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
    ])

    const analytics = {
      totalStudents,
      totalTeachers,
      totalGroups,
      totalCourses,
      totalRevenue: totalRevenue._sum.amount || 0,
      recentEnrollments,
      recentPayments,
      timeRange,
      generatedAt: new Date().toISOString(),
    }

    cache.set(cacheKey, analytics, 10 * 60 * 1000) // Cache for 10 minutes
    return analytics
  }

  // Invalidate cache when data changes
  static invalidateStudentCache() {
    const keys = Array.from(cache['cache'].keys()).filter(key => key.startsWith('students:'))
    keys.forEach(key => cache.delete(key))
  }

  static invalidateTeacherCache() {
    cache.delete('teachers:all')
  }

  static invalidateGroupCache() {
    cache.delete('groups:all')
  }

  static invalidateAnalyticsCache() {
    const keys = Array.from(cache['cache'].keys()).filter(key => key.startsWith('analytics:'))
    keys.forEach(key => cache.delete(key))
  }
}

// Database connection optimization
export class DatabaseOptimizer {
  // Connection pooling is handled by Prisma, but we can optimize queries
  
  static async batchInsert<T>(model: any, data: T[], batchSize = 100): Promise<void> {
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize)
      await model.createMany({
        data: batch,
        skipDuplicates: true,
      })
    }
  }

  static async batchUpdate<T>(
    model: any,
    updates: Array<{ where: any; data: T }>
  ): Promise<void> {
    const promises = updates.map(({ where, data }) =>
      model.update({ where, data })
    )
    await Promise.all(promises)
  }

  // Optimize queries with proper indexing suggestions
  static getIndexSuggestions() {
    return [
      'CREATE INDEX IF NOT EXISTS idx_users_phone ON "User"(phone);',
      'CREATE INDEX IF NOT EXISTS idx_users_email ON "User"(email);',
      'CREATE INDEX IF NOT EXISTS idx_students_level ON "Student"(level);',
      'CREATE INDEX IF NOT EXISTS idx_enrollments_status ON "Enrollment"(status);',
      'CREATE INDEX IF NOT EXISTS idx_payments_status ON "Payment"(status);',
      'CREATE INDEX IF NOT EXISTS idx_payments_due_date ON "Payment"("dueDate");',
      'CREATE INDEX IF NOT EXISTS idx_classes_date ON "Class"(date);',
      'CREATE INDEX IF NOT EXISTS idx_attendance_status ON "Attendance"(status);',
    ]
  }
}

// Performance monitoring
export class PerformanceMonitor {
  private static metrics = new Map<string, number[]>()

  static startTimer(operation: string): () => void {
    const start = performance.now()
    
    return () => {
      const duration = performance.now() - start
      this.recordMetric(operation, duration)
    }
  }

  static recordMetric(operation: string, duration: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, [])
    }
    
    const metrics = this.metrics.get(operation)!
    metrics.push(duration)
    
    // Keep only last 100 measurements
    if (metrics.length > 100) {
      metrics.shift()
    }
  }

  static getMetrics(operation: string) {
    const metrics = this.metrics.get(operation) || []
    if (metrics.length === 0) return null

    const avg = metrics.reduce((sum, val) => sum + val, 0) / metrics.length
    const min = Math.min(...metrics)
    const max = Math.max(...metrics)
    
    return {
      operation,
      count: metrics.length,
      average: Math.round(avg * 100) / 100,
      min: Math.round(min * 100) / 100,
      max: Math.round(max * 100) / 100,
    }
  }

  static getAllMetrics() {
    const operations = Array.from(this.metrics.keys())
    return operations.map(op => this.getMetrics(op)).filter(Boolean)
  }

  static clearMetrics(): void {
    this.metrics.clear()
  }
}

// Cleanup function to run periodically
export function runPerformanceCleanup(): void {
  cache.cleanup()

  // Log performance metrics only in development
  if (process.env.NODE_ENV === 'development') {
    const metrics = PerformanceMonitor.getAllMetrics()
    if (metrics.length > 0) {
      console.log('Performance Metrics:', metrics)
    }
  }
}

// Export cache instance for direct access if needed
export { cache }

// Auto-cleanup every 10 minutes
if (typeof window === 'undefined') {
  setInterval(runPerformanceCleanup, 10 * 60 * 1000)
}
